import { Metadata } from 'next'

interface SEOConfig {
  title: string
  description: string
  keywords?: string[]
  canonical?: string
  ogImage?: string
  ogType?: 'website' | 'article' | 'profile'
  publishedTime?: string
  modifiedTime?: string
  authors?: string[]
  section?: string
  tags?: string[]
  noIndex?: boolean
}

const defaultKeywords = [
  'GirlCode',
  'GirlCode Movement', 
  'women in tech',
  'digital skills training',
  'career mentorship',
  'STEM education',
  'gender equality',
  'empowerment',
  'nonprofit',
  'Kenya',
  'Africa',
  'SDG',
  'sustainable development goals'
]

const baseUrl = 'https://girlcodemovement.org'
const defaultOGImage = `${baseUrl}/images/og-default.jpg`
const organizationSchema = {
  '@context': 'https://schema.org',
  '@type': 'Organization',
  name: 'GirlCode Movement',
  alternateName: 'GirlCode',
  url: baseUrl,
  logo: `${baseUrl}/images/logo.png`,
  description: 'Empowering young girls and women through digital skills training, career mentorship, and personal development programs.',
  foundingDate: '2018',
  sameAs: [
    'https://twitter.com/girlcode',
    'https://facebook.com/girlcodemovement',
    'https://linkedin.com/company/girlcode',
    'https://instagram.com/girlcodemovement'
  ],
  contactPoint: {
    '@type': 'ContactPoint',
    contactType: 'Customer Service',
    email: '<EMAIL>'
  },
  address: {
    '@type': 'PostalAddress',
    addressCountry: 'KE',
    addressLocality: 'Nairobi'
  }
}

export function generateSEOMetadata(config: SEOConfig): Metadata {
  const {
    title,
    description,
    keywords = [],
    canonical,
    ogImage = defaultOGImage,
    ogType = 'website',
    publishedTime,
    modifiedTime,
    authors,
    section,
    tags,
    noIndex = false
  } = config

  const fullTitle = title.includes('GirlCode') ? title : `${title} | GirlCode Movement`
  const allKeywords = [...defaultKeywords, ...keywords]
  const canonicalUrl = canonical || baseUrl

  const metadata: Metadata = {
    title: fullTitle,
    description,
    keywords: allKeywords,
    authors: authors ? authors.map(author => ({ name: author })) : [{ name: 'GirlCode Movement' }],
    creator: 'GirlCode Movement',
    publisher: 'GirlCode Movement',
    robots: noIndex ? 'noindex, nofollow' : 'index, follow, max-image-preview:large, max-snippet:-1, max-video-preview:-1',
    alternates: {
      canonical: canonicalUrl,
    },
    openGraph: {
      title: fullTitle,
      description,
      url: canonicalUrl,
      siteName: 'GirlCode Movement',
      images: [
        {
          url: ogImage,
          width: 1200,
          height: 630,
          alt: title,
        },
      ],
      type: ogType,
      locale: 'en_US',
      ...(publishedTime && { publishedTime }),
      ...(modifiedTime && { modifiedTime }),
      ...(authors && { authors: authors }),
      ...(section && { section }),
      ...(tags && { tags }),
    },
    twitter: {
      card: 'summary_large_image',
      title: fullTitle,
      description,
      creator: '@girlcode',
      images: [ogImage],
    },
    other: {
      'application-name': 'GirlCode Movement',
      'apple-mobile-web-app-title': 'GirlCode',
      'msapplication-TileColor': '#FF0066',
      'theme-color': '#FF0066',
    }
  }

  return metadata
}

export function generateBreadcrumbSchema(items: Array<{ name: string; url: string }>) {
  return {
    '@context': 'https://schema.org',
    '@type': 'BreadcrumbList',
    itemListElement: items.map((item, index) => ({
      '@type': 'ListItem',
      position: index + 1,
      name: item.name,
      item: `${baseUrl}${item.url}`
    }))
  }
}

export function generateArticleSchema(article: {
  headline: string
  description: string
  datePublished: string
  dateModified?: string
  author: string
  image: string
  url: string
  category?: string
  tags?: string[]
}) {
  return {
    '@context': 'https://schema.org',
    '@type': 'Article',
    headline: article.headline,
    description: article.description,
    image: article.image,
    datePublished: article.datePublished,
    dateModified: article.dateModified || article.datePublished,
    author: {
      '@type': 'Person',
      name: article.author
    },
    publisher: organizationSchema,
    mainEntityOfPage: {
      '@type': 'WebPage',
      '@id': article.url
    },
    ...(article.category && { articleSection: article.category }),
    ...(article.tags && { keywords: article.tags.join(', ') })
  }
}

export function generateEventSchema(event: {
  name: string
  description: string
  startDate: string
  endDate?: string
  location: string
  image: string
  url: string
  organizer?: string
  price?: string
}) {
  return {
    '@context': 'https://schema.org',
    '@type': 'Event',
    name: event.name,
    description: event.description,
    startDate: event.startDate,
    ...(event.endDate && { endDate: event.endDate }),
    location: {
      '@type': 'Place',
      name: event.location
    },
    image: event.image,
    url: event.url,
    organizer: {
      '@type': 'Organization',
      name: event.organizer || 'GirlCode Movement',
      url: baseUrl
    },
    ...(event.price && {
      offers: {
        '@type': 'Offer',
        price: event.price === 'Free' ? '0' : event.price,
        priceCurrency: 'KES',
        availability: 'https://schema.org/InStock'
      }
    })
  }
}

export { organizationSchema }
