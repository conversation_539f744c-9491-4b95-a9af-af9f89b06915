# G-Code

This is a [Next.js](https://nextjs.org) project for G-Code, a platform dedicated to empowering women in technology.

## Getting Started

First, run the development server:

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

## Features

- Modern tech stack using Next.js 15
- Responsive design
- User authentication
- Interactive learning modules
- Community features

## Tech Stack

- Next.js 15
- TypeScript
- Tailwind CSS
- [Geist <PERSON>ont](https://vercel.com/font) for typography

## Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the LICENSE file for details.
