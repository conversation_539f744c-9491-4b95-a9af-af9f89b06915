// types/sdg.ts
export interface Program {
    id: string;
    name: string;
    overview: string;
    description: string;
    curriculum?: string[];
    support?: string[];
    impact?: string[];
    campaigns?: string[];
    benefits?: string[];
    details?: string[];
    outcomes?: string[];
    image: string;
    link: string;
  }
  
  export interface Testimonial {
    id: string;
    quote: string;
    author: string;
    role: string;
    image: string;
  }
  
  export interface ImpactMetric {
    id: string;
    title: string;
    value: string;
    description: string;
    icon: string;
  }
  
  export interface Resource {
    id: string;
    title: string;
    description: string;
    type: "PDF" | "Guide" | "Video" | "Tool" | "Downloads" | "Digital" | "Contacts";
    image: string;
    link: string;
    fileSize?: string;
    color: string;
    iconBgColor: string;
  }
  
  export interface Partner {
    id: string;
    name: string;
    description: string;
    logo: string;
    website: string;
  }
  
  export interface SuccessStory {
    id: string;
    title: string;
    summary: string;
    story: string;
    personName: string;
    personRole: string;
    personImage: string;
    impact: string[];
    featured: boolean;
  }
  
  export interface FAQ {
    question: string;
    answer: string;
  }
  
  export interface RelatedSDG {
    number: number;
    title: string;
    connection: string;
    image: string;
  }
  
  export interface SDGData {
    number: number;
    title: string;
    shortTitle: string;
    description: string;
    longDescription: string;
    approach: string;
    color: string;
    bgColor: string;
    borderColor: string;
    iconBgColor: string;
    textColor: string;
    buttonColor: string;
    icon: string;
    heroImage: string;
    detailImage: string;
    programs: Program[];
    impactMetrics: ImpactMetric[];
    testimonials: Testimonial[];
    resources: Resource[];
    partners: Partner[];
    successStories: SuccessStory[];
    faqs: FAQ[];
    relatedSDGs: RelatedSDG[];
  }