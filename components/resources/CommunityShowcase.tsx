import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import Image from "next/image";
import { ChevronRight, Star, Award, Users, Bookmark } from "lucide-react";

// Project categories for filtering
const projectCategories = [
  "All Projects",
  "Web Development",
  "Mobile Apps",
  "3D Printing",
  "Hardware",
];

// Enhanced project data with additional fields
const projectsData = [
  {
    title: "Health Monitoring App",
    description: "Mobile application for personal wellness tracking",
    image: "/images/certificate-image.jpg",
    link: "/showcase/health-app",
    category: "Mobile Apps",
    student: "<PERSON>ina <PERSON>jeri",
    studentImage: "/images/inspiring-change.jpg",
    isFeatured: true,
    sdgTag: "SDG 3: Good Health",
  },
  {
    title: "Small Business Website",
    description: "Digital presence for local women entrepreneurs",
    image: "/images/inspiring-change.jpg",
    link: "/showcase/business-website",
    category: "Web Development",
    student: "<PERSON>",
    studentImage: "/images/empowering-image.JPG",
    isFeatured: true,
    sdgTag: "SDG 5: Gender Equality",
  },
  {
    title: "3D Printed Assistive Devices",
    description: "Sustainable healthcare solution designs",
    image: "/images/image-3.jpg",
    link: "/showcase/3d-assistive-devices",
    category: "3D Printing",
    student: "Simon Kimathi",
    studentImage: "/images/education-image.png",
    isFeatured: false,
    sdgTag: "SDG 3: Good Health",
  },
  {
    title: "Community Water Monitor",
    description: "Environmental sensor project for clean water",
    image: "/images/education-image.png",
    link: "/showcase/water-monitor",
    category: "Hardware",
    student: "Daniel Ochieng",
    studentImage: "/images/certificate-image.jpg",
    isFeatured: false,
    sdgTag: "SDG 13: Climate Action",
  },
];

// Enhanced Featured Project Card Component
const FeaturedProjectCard = ({
  title,
  description,
  imageSrc,
  link = "#",
  category,
  student,
  studentImage,
  isFeatured,
  sdgTag,
}: {
  title: string;
  description: string;
  imageSrc: string;
  link?: string;
  category: string;
  student: string;
  studentImage: string;
  isFeatured: boolean;
  sdgTag: string;
}) => (
  <div className="bg-white rounded-xl shadow-sm overflow-hidden hover:shadow-md transition-all duration-300 group border border-gray-100 h-full flex flex-col">
    <div className="relative h-56 w-full overflow-hidden">
      <Image
        src={imageSrc}
        alt={title}
        fill
        className="object-cover group-hover:scale-105 transition-transform duration-500"
        onError={(e) => {
          e.currentTarget.src = "/images/certificate-image.jpg";
        }}
      />
      <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/30 to-transparent"></div>

      {/* Category badge */}
      <div className="absolute top-4 left-4">
        <span className="bg-black/30 backdrop-blur-sm text-white text-xs px-3 py-1.5 rounded-full">
          {category}
        </span>
      </div>

      {/* Featured badge */}
      {isFeatured && (
        <div className="absolute top-4 right-4">
          <span className="bg-primary/90 text-white text-xs px-3 py-1.5 rounded-full flex items-center gap-1.5">
            <Star className="w-3 h-3 fill-white" />
            Featured
          </span>
        </div>
      )}

      {/* SDG Tag */}
      <div className="absolute bottom-16 right-4">
        <span className="bg-white/80 backdrop-blur-sm text-gray-700 text-xs px-2.5 py-1 rounded-full">
          {sdgTag}
        </span>
      </div>

      <div className="absolute bottom-4 left-4 text-white">
        <h3 className="text-xl font-bold mb-1">{title}</h3>
        <p className="text-sm text-white/90 line-clamp-2">{description}</p>
      </div>
    </div>

    <div className="p-5 flex flex-col gap-4 flex-grow">
      <div className="flex items-center gap-3">
        <div className="relative w-8 h-8 rounded-full overflow-hidden border-2 border-white">
          <Image
            src={studentImage}
            alt={student}
            fill
            className="object-cover"
            onError={(e) => {
              e.currentTarget.src = "/images/certificate-image.jpg";
            }}
          />
        </div>
        <div>
          <p className="text-sm font-medium text-gray-900">{student}</p>
          <p className="text-xs text-gray-500">Student Developer</p>
        </div>
      </div>

      <p className="text-gray-600 text-sm flex-grow">
        {description} This project showcases skills learned through GirlCode's
        programs.
      </p>
    </div>

    <div className="p-4 flex justify-between items-center border-t border-gray-100 bg-gray-50">
      <Button
        variant="ghost"
        size="sm"
        className="text-gray-500 hover:text-gray-700"
      >
        <Bookmark className="h-4 w-4 mr-1" />
        <span className="text-xs">Save</span>
      </Button>
      <Button variant="ghost" className="text-primary hover:bg-primary/5">
        <Link href={link} className="flex items-center gap-1">
          <span>View Details</span>
          <ChevronRight className="h-4 w-4 group-hover:translate-x-1 transition-transform" />
        </Link>
      </Button>
    </div>
  </div>
);

// Featured Project with Larger Card
const FeaturedLargeProject = ({
  project,
}: {
  project: (typeof projectsData)[0];
}) => (
  <div className="bg-white rounded-xl shadow-md overflow-hidden hover:shadow-lg transition-all group border border-gray-100 mb-8">
    <div className="grid md:grid-cols-2 h-full">
      {/* Image column */}
      <div className="relative h-72 md:h-auto">
        <Image
          src={project.image}
          alt={project.title}
          fill
          className="object-cover group-hover:scale-105 transition-transform duration-500"
          onError={(e) => {
            e.currentTarget.src = "/images/certificate-image.jpg";
          }}
        />
        <div className="absolute inset-0 bg-gradient-to-r from-black/70 to-transparent md:via-black/40 md:to-transparent"></div>

        {/* Featured badge */}
        <div className="absolute top-4 left-4 md:left-4">
          <span className="bg-primary text-white text-xs px-3 py-1.5 rounded-full flex items-center gap-1.5">
            <Award className="w-3.5 h-3.5" />
            <span>Project of the Month</span>
          </span>
        </div>
      </div>

      {/* Content column */}
      <div className="p-8 flex flex-col justify-between">
        <div>
          <div className="flex justify-between items-start mb-4">
            <div>
              <h3 className="text-2xl font-bold mb-2 text-gray-900 group-hover:text-primary transition-colors">
                {project.title}
              </h3>
              <div className="flex items-center gap-2 mb-3">
                <span className="bg-gray-100 text-gray-700 text-xs px-2.5 py-1 rounded-full">
                  {project.category}
                </span>
                <span className="bg-blue-50 text-blue-700 text-xs px-2.5 py-1 rounded-full">
                  {project.sdgTag}
                </span>
              </div>
            </div>
          </div>

          <p className="text-gray-600 mb-6">
            {project.description} This student project demonstrates innovative
            problem-solving using technology to address real-world challenges
            facing communities.
          </p>

          <div className="bg-gray-50 p-4 rounded-lg mb-4">
            <h4 className="text-sm font-medium mb-2">Project Impact</h4>
            <p className="text-sm text-gray-600">
              This solution helps community members monitor and improve their
              health outcomes through accessible technology, creating
              sustainable change.
            </p>
          </div>
        </div>

        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="relative w-10 h-10 rounded-full overflow-hidden border-2 border-white">
              <Image
                src={project.studentImage}
                alt={project.student}
                fill
                className="object-cover"
                onError={(e) => {
                  e.currentTarget.src = "/images/certificate-image.jpg";
                }}
              />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-900">
                {project.student}
              </p>
              <p className="text-xs text-gray-500">Student Developer</p>
            </div>
          </div>

          <Button className="bg-primary hover:bg-primary/90 text-white">
            <Link href={project.link} className="flex items-center gap-1">
              <span>View Project</span>
              <ChevronRight className="h-4 w-4 group-hover:translate-x-1 transition-transform" />
            </Link>
          </Button>
        </div>
      </div>
    </div>
  </div>
);

export default function CommunityShowcase() {
  // Get featured projects
  const featuredProjects = projectsData.filter((project) => project.isFeatured);
  // Get regular projects
  const regularProjects = projectsData.filter((project) => !project.isFeatured);

  return (
    <section className="py-20 bg-gray-50">
      <div className="container mx-auto px-6 max-w-7xl">
        {/* Enhanced Section Header */}
        <div className="text-center mb-12">
          <div className="inline-flex items-center gap-2 bg-primary/10 py-1 px-3 rounded-full w-fit mx-auto mb-4">
            <Users className="w-4 h-4 text-primary" />
            <span className="text-sm font-medium text-primary">
              Community Showcase
            </span>
          </div>
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            Student Projects Gallery
          </h2>
          <p className="text-gray-700 max-w-2xl mx-auto md:text-lg">
            Explore innovative projects created by young technologists solving
            real-world challenges through our programs.
          </p>
        </div>

        {/* Category Filters */}
        <div className="flex flex-wrap justify-center gap-3 mb-10">
          {projectCategories.map((category, index) => (
            <button
              key={index}
              className={`px-4 py-2 rounded-full text-sm font-medium transition-colors ${
                index === 0
                  ? "bg-primary text-white"
                  : "bg-white text-gray-700 hover:bg-gray-100 border border-gray-200"
              }`}
            >
              {category}
            </button>
          ))}
        </div>

        {/* Featured Project (Larger Card) */}
        {featuredProjects.length > 0 && (
          <FeaturedLargeProject project={featuredProjects[0]} />
        )}

        {/* Regular Projects Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
          {/* Include featured projects (except the first one) */}
          {featuredProjects.slice(1).map((project, index) => (
            <FeaturedProjectCard
              key={`featured-${index}`}
              title={project.title}
              description={project.description}
              imageSrc={project.image}
              link={project.link}
              category={project.category}
              student={project.student}
              studentImage={project.studentImage}
              isFeatured={project.isFeatured}
              sdgTag={project.sdgTag}
            />
          ))}

          {/* Regular projects */}
          {regularProjects.map((project, index) => (
            <FeaturedProjectCard
              key={`regular-${index}`}
              title={project.title}
              description={project.description}
              imageSrc={project.image}
              link={project.link}
              category={project.category}
              student={project.student}
              studentImage={project.studentImage}
              isFeatured={project.isFeatured}
              sdgTag={project.sdgTag}
            />
          ))}
        </div>

        {/* "View All" Button */}
        <div className="text-center mt-12">
          <Button
            variant="outline"
            className="border-primary text-primary hover:bg-primary/5 px-6 py-2.5"
          >
            <Link href="/showcase" className="flex items-center gap-2">
              <span>View All Projects</span>
              <ChevronRight className="h-4 w-4" />
            </Link>
          </Button>
        </div>
      </div>
    </section>
  );
}
