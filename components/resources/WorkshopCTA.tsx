import Link from "next/link";
import Image from "next/image";
import { Button } from "@/components/ui/button";

export default function WorkshopCTA() {
    return (
        <div className="w-full py-20 px-5 md:px-10 lg:px-16 bg-white">
            <div className="max-w-7xl mx-auto">
                <div className="relative rounded-2xl overflow-hidden bg-white shadow-lg">
                    {/* Background gradient */}
                    <div className="absolute inset-0 bg-gradient-to-r from-primary/10 via-purple-50 to-white -z-10"></div>

                    {/* Decorative elements */}
                    <div className="absolute top-0 right-0 w-64 h-64 bg-primary/5 rounded-full -translate-y-1/2 translate-x-1/2 -z-10"></div>
                    <div className="absolute bottom-0 left-0 w-48 h-48 bg-purple-100/20 rounded-full translate-y-1/3 -translate-x-1/4 -z-10"></div>

                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-0">
                        {/* Content side */}
                        <div className="p-8 md:p-12 lg:p-16 flex flex-col justify-center">
                            <div className="inline-flex items-center gap-2 bg-primary/10 py-1 px-3 rounded-full w-fit mb-6">
                                <span className="text-sm font-medium text-primary">
                                    Upcoming Event
                                </span>
                            </div>

                            <h2 className="font-bold text-3xl md:text-4xl lg:text-5xl mb-6 leading-tight">
                                Join Our Next Workshop
                            </h2>

                            <p className="text-base md:text-lg text-gray-700 mb-8 max-w-lg">
                                Take your skills to the next level with our interactive,
                                hands-on workshops designed to inspire and empower. Our expert
                                facilitators will guide you through practical learning
                                experiences.
                            </p>

                            <div className="flex flex-col sm:flex-row gap-4 mb-6">
                                <Button className="bg-primary hover:bg-primary/90 px-6 py-3 text-white font-medium rounded-lg">
                                    <Link href={"/events"} className="flex items-center gap-2">
                                        View Event
                                        <svg
                                            xmlns="http://www.w3.org/2000/svg"
                                            width="16"
                                            height="16"
                                            viewBox="0 0 24 24"
                                            fill="none"
                                            stroke="currentColor"
                                            strokeWidth="2"
                                            strokeLinecap="round"
                                            strokeLinejoin="round"
                                        >
                                            <path d="M5 12h14M12 5l7 7-7 7" />
                                        </svg>
                                    </Link>
                                </Button>
                                <Button
                                    variant={"outline"}
                                    className="border-gray-300 hover:border-primary hover:text-primary px-6 py-3 font-medium rounded-lg"
                                >
                                    <Link href={"/workshops"}>View Workshop Schedule</Link>
                                </Button>
                            </div>

                            <div className="flex items-center mt-6">
                                <div className="flex -space-x-2 mr-3">
                                    <div className="w-8 h-8 rounded-full border-2 border-white bg-gray-200 overflow-hidden">
                                        <Image
                                            src="/images/inspiring-change.jpg"
                                            alt="Participant"
                                            width={32}
                                            height={32}
                                            className="w-full h-full object-cover"
                                        />
                                    </div>
                                    <div className="w-8 h-8 rounded-full border-2 border-white bg-gray-200 overflow-hidden">
                                        <Image
                                            src="/images/empowering-image.JPG"
                                            alt="Participant"
                                            width={32}
                                            height={32}
                                            className="w-full h-full object-cover"
                                        />
                                    </div>
                                    <div className="w-8 h-8 rounded-full border-2 border-white bg-gray-200 overflow-hidden">
                                        <Image
                                            src="/images/certificate-image.jpg"
                                            alt="Participant"
                                            width={32}
                                            height={32}
                                            className="w-full h-full object-cover"
                                        />
                                    </div>
                                </div>
                                <span className="text-sm text-gray-600">
                                    24 people registered
                                </span>
                            </div>
                        </div>

                        {/* Image side */}
                        <div className="relative h-64 md:h-80 lg:h-full min-h-[320px]">
                            <Image
                                src={"/images/empowering-image.JPG"}
                                alt="Workshop participants"
                                fill
                                className="object-cover"
                            />

                            {/* Image overlay with info */}
                            <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-transparent to-transparent flex flex-col justify-end p-6">
                                <div className="bg-white/90 backdrop-blur-sm rounded-lg p-3 shadow-sm inline-block w-auto self-start">
                                    <div className="flex items-center gap-3">
                                        <div className="bg-primary/20 p-2 rounded-full">
                                            <svg
                                                xmlns="http://www.w3.org/2000/svg"
                                                width="16"
                                                height="16"
                                                viewBox="0 0 24 24"
                                                fill="none"
                                                stroke="currentColor"
                                                strokeWidth="2"
                                                strokeLinecap="round"
                                                strokeLinejoin="round"
                                                className="text-primary"
                                            >
                                                <rect
                                                    x="3"
                                                    y="4"
                                                    width="18"
                                                    height="18"
                                                    rx="2"
                                                    ry="2"
                                                ></rect>
                                                <line x1="16" y1="2" x2="16" y2="6"></line>
                                                <line x1="8" y1="2" x2="8" y2="6"></line>
                                                <line x1="3" y1="10" x2="21" y2="10"></line>
                                            </svg>
                                        </div>
                                        <div>
                                            <p className="text-xs font-semibold">March 18, 2025</p>
                                            <p className="text-xs text-gray-600">
                                                9:00 AM - 4:00 PM
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    )
}




