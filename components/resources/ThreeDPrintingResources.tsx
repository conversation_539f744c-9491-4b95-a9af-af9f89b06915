"use client";

import React from "react";
import Image from "next/image";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import UniversalCard from "../ui/universal-card";

export default function ThreeDPrintingResources() {
  const threeDResources = [
    {
      id: "3d-printing-basics",
      title: "3D Printing Basics",
      description: "Begin your journey into eco-friendly 3D printing",
      image: "/images/image-3.jpg",
      link: "/resources/3d-printing-basics",
      category: "3D Printing",
      color: "bg-green-600",
      bgColor: "bg-green-50",
    },
    {
      id: "cad-design",
      title: "CAD Design Mastery",
      description: "Learn advanced sustainable 3D design techniques",
      image: "/images/education-image.png",
      link: "/resources/cad-design",
      category: "3D Printing",
      color: "bg-green-600",
      bgColor: "bg-green-50",
    },
    {
      id: "troubleshooting",
      title: "Troubleshooting Guide",
      description: "Navigate model challenges with expert tips",
      image: "/images/certificate-image.jpg",
      link: "/resources/3d-troubleshooting",
      category: "3D Printing",
      color: "bg-green-600",
      bgColor: "bg-green-50",
    },
  ];

  return (
    <div
      id="3d-printing-section"
      className="w-full py-20 px-5 md:px-10 lg:px-16 bg-gray-50"
    >
      <div className="max-w-7xl mx-auto">
        <div className="flex flex-col md:flex-row items-start justify-between gap-8 mb-12">
          <div>
            <div className="flex items-center gap-3 mb-4">
              <div className="bg-green-100 p-3 rounded-full">
                <Image
                  src="/vectors/github.svg"
                  alt="3D printing resources"
                  height={24}
                  width={24}
                  className="w-6 h-6"
                  onError={(e) => {
                    e.currentTarget.src = "/vectors/github.svg";
                  }}
                />
              </div>
              <span className="text-sm font-medium text-green-600">
                3D Printing Resources
              </span>
            </div>
            <h2 className="font-bold text-3xl xl:text-4xl leading-tight mb-2">
              Essential Resources for 3D Printing
            </h2>
          </div>
          <p className="text-base md:text-lg text-gray-700 max-w-xl">
            Dive into the world of sustainable 3D printing with our carefully
            selected resources to help you learn, design, and solve challenges.
          </p>
        </div>

        <div className="grid md:grid-cols-3 gap-6">
          {threeDResources.map((resource) => (
            <UniversalCard
              key={resource.id}
              type="resource"
              data={resource}
              layout="grid"
              showImage={true}
              showMeta={true}
            />
          ))}
        </div>
      </div>
    </div>
  );
}
