import Image from "next/image";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ChevronRight } from "lucide-react";

export default function TeenSelfHelp() {
    return (

        <div
            id="mental-health-section"
            className="w-full py-20 px-5 md:px-10 lg:px-16 bg-white"
        >
            <div className="max-w-7xl mx-auto">
                <div className="flex flex-col xl:flex-row xl:items-center gap-12 xl:gap-16">
                    {/* Content */}
                    <div className="w-full xl:w-1/2 flex flex-col gap-6">
                        <div className="inline-flex items-center gap-2 bg-primary/10 py-1 px-3 rounded-full w-fit mb-2">
                            <span className="text-sm font-medium text-primary">
                                Featured Resource
                            </span>
                        </div>

                        <h2 className="font-bold text-3xl md:text-4xl xl:text-5xl leading-tight">
                            Teen Self-Help Handbook
                        </h2>

                        <p className="text-base md:text-lg text-gray-700">
                            A comprehensive guide designed to support personal growth,
                            mental well-being, and self-discovery for teens. This resource
                            provides practical strategies for navigating digital challenges
                            while maintaining positive mental health.
                        </p>

                        <div className="flex flex-col gap-6 my-2">
                            <div className="bg-gray-50 rounded-lg p-6 border border-gray-100">
                                <h3 className="font-bold text-lg mb-3">What{"'"}s Inside:</h3>
                                <ul className="space-y-2">
                                    <li className="flex items-start gap-3">
                                        <div className="flex-shrink-0 w-6 h-6 bg-primary/10 rounded-full flex items-center justify-center mt-0.5">
                                            <div className="w-2 h-2 bg-primary rounded-full"></div>
                                        </div>
                                        <p className="text-gray-700">
                                            Mental wellness strategies for teens
                                        </p>
                                    </li>
                                    <li className="flex items-start gap-3">
                                        <div className="flex-shrink-0 w-6 h-6 bg-primary/10 rounded-full flex items-center justify-center mt-0.5">
                                            <div className="w-2 h-2 bg-primary rounded-full"></div>
                                        </div>
                                        <p className="text-gray-700">
                                            Digital balance techniques
                                        </p>
                                    </li>
                                    <li className="flex items-start gap-3">
                                        <div className="flex-shrink-0 w-6 h-6 bg-primary/10 rounded-full flex items-center justify-center mt-0.5">
                                            <div className="w-2 h-2 bg-primary rounded-full"></div>
                                        </div>
                                        <p className="text-gray-700">
                                            Self-discovery exercises and journaling prompts
                                        </p>
                                    </li>
                                    <li className="flex items-start gap-3">
                                        <div className="flex-shrink-0 w-6 h-6 bg-primary/10 rounded-full flex items-center justify-center mt-0.5">
                                            <div className="w-2 h-2 bg-primary rounded-full"></div>
                                        </div>
                                        <p className="text-gray-700">
                                            Building healthy tech habits
                                        </p>
                                    </li>
                                </ul>
                            </div>
                        </div>

                        <div className="flex flex-col sm:flex-row gap-4 mt-2">
                            <Button className="bg-[#4c9f38] hover:opacity-90 text-white">
                                <Link href="/resources/teen-handbook">Access Handbook</Link>
                            </Button>
                            <Button
                                variant="outline"
                                className="border-gray-300 hover:border-primary hover:text-primary"
                            >
                                <Link href="/contact" className="flex items-center gap-2">
                                    <span>Request Printed Copy</span>
                                    <ChevronRight className="h-4 w-4" />
                                </Link>
                            </Button>
                        </div>
                    </div>

                    {/* Image */}
                    <div className="w-full xl:w-1/2 relative">
                        <div className="relative rounded-xl overflow-hidden shadow-lg">
                            <Image
                                src="/images/inspiring-change.jpg"
                                alt="Teen Self-Help Handbook"
                                width={600}
                                height={500}
                                className="w-full h-auto object-cover"
                            />
                            <div className="absolute inset-0 bg-gradient-to-t from-black/40 via-transparent to-transparent"></div>
                        </div>

                        {/* Authors card */}
                        <div className="absolute -bottom-5 -right-5 bg-white p-4 rounded-lg shadow-lg border border-gray-100 hidden md:block">
                            <p className="text-sm font-semibold mb-2">
                                Written by experts in teen psychology
                            </p>
                            <div className="flex items-center gap-3">
                                <div className="bg-purple-100 p-2 rounded-full">
                                    <svg
                                        xmlns="http://www.w3.org/2000/svg"
                                        width="16"
                                        height="16"
                                        viewBox="0 0 24 24"
                                        fill="none"
                                        stroke="currentColor"
                                        strokeWidth="2"
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        className="text-purple-600"
                                    >
                                        <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                                        <circle cx="12" cy="7" r="4"></circle>
                                    </svg>
                                </div>
                                <div>
                                    <p className="text-xs text-gray-600">
                                        In collaboration with mental health professionals
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    )
}