"use client";

import Image from "next/image";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import {
  ChevronRight,
  BookOpen,
  Code,
  Printer,
  Heart,
  Users,
} from "lucide-react";
import { useState } from "react";

// Resource categories with consistent styling and enhanced icons
const resourceCategories = [
  {
    id: "coding",
    title: "Coding Resources",
    description: "Tools and guides to support your programming journey",
    icon: <Code className="w-6 h-6 text-blue-600" />,
    color: "bg-blue-50",
    borderColor: "border-blue-100",
    iconBgColor: "bg-blue-100",
    textColor: "text-blue-600",
    buttonBg: "bg-[#c5192d]", // SDG 4 color
    badgeText: "Most Popular",
    items: [
      {
        title: "Learn to Code",
        description: "Interactive platforms for coding beginners",
        image: "/images/education-image.png",
        link: "/resources/learn-to-code",
      },
      {
        title: "Essential Books",
        description: "Curated coding guides and references",
        image: "/images/certificate-image.jpg",
        link: "/resources/coding-books",
      },
      {
        title: "Coding Challenges",
        description: "Inclusive beginner-friendly challenges",
        image: "/images/empowering-image.JPG",
        link: "/resources/coding-challenges",
      },
      {
        title: "Community Network",
        description: "Join our supportive coding community",
        image: "/images/inspiring-change.jpg",
        link: "/resources/coding-community",
      },
    ],
  },
  {
    id: "3d-printing",
    title: "3D Printing Resources",
    description:
      "Materials and guides for sustainable 3D design and manufacturing",
    icon: <Printer className="w-6 h-6 text-green-600" />,
    color: "bg-green-50",
    borderColor: "border-green-100",
    iconBgColor: "bg-green-100",
    textColor: "text-green-600",
    buttonBg: "bg-[#3f7e44]", // SDG 13 color
    badgeText: "Eco-Friendly",
    items: [
      {
        title: "3D Printing Basics",
        description: "Begin your journey into eco-friendly 3D printing",
        image: "/images/image-3.jpg",
        link: "/resources/3d-printing-basics",
      },
      {
        title: "CAD Design Mastery",
        description: "Learn advanced sustainable 3D design techniques",
        image: "/images/education-image.png",
        link: "/resources/cad-design",
      },
      {
        title: "Troubleshooting Guide",
        description: "Navigate model challenges with expert tips",
        image: "/images/certificate-image.jpg",
        link: "/resources/3d-troubleshooting",
      },
    ],
  },
  {
    id: "mental-health",
    title: "Mental Health Resources",
    description: "Supporting teen well-being in the digital age",
    icon: <Heart className="w-6 h-6 text-purple-600" />,
    color: "bg-purple-50",
    borderColor: "border-purple-100",
    iconBgColor: "bg-purple-100",
    textColor: "text-purple-600",
    buttonBg: "bg-[#4c9f38]", // SDG 3 color
    badgeText: "Well-Being",
    items: [
      {
        title: "Teen Self-Help Handbook",
        description:
          "Comprehensive guide supporting mental well-being and self-discovery",
        image: "/images/inspiring-change.jpg",
        link: "/resources/teen-handbook",
      },
      {
        title: "Digital Wellness",
        description: "Strategies for maintaining balance with technology",
        image: "/images/empowering-image.JPG",
        link: "/resources/digital-wellness",
      },
    ],
  },
  {
    id: "gender-equality",
    title: "Gender Equality Resources",
    description: "Materials supporting women and girls in tech and beyond",
    icon: <Users className="w-6 h-6 text-pink-600" />,
    color: "bg-pink-50",
    borderColor: "border-pink-100",
    iconBgColor: "bg-pink-100",
    textColor: "text-pink-600",
    buttonBg: "bg-[#ff3a21]", // SDG 5 color
    badgeText: "Empowerment",
    items: [
      {
        title: "Advocacy Tools",
        description: "Resources for promoting gender equality in tech",
        image: "/images/education-image.png",
        link: "/resources/advocacy-tools",
      },
      {
        title: "Safe Spaces Guide",
        description: "Creating inclusive environments for learning",
        image: "/images/certificate-image.jpg",
        link: "/resources/safe-spaces",
      },
    ],
  },
];

export default function ResourceCategories() {
  const [activeCategory, setActiveCategory] = useState("coding");
  const currentCategory =
    resourceCategories.find((cat) => cat.id === activeCategory) ||
    resourceCategories[0];

  return (
    <section id="resources" className="py-20 bg-gray-50">
      <div className="container mx-auto px-6 lg:px-8 max-w-7xl">
        {/* Enhanced Section Header */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center gap-2 bg-primary/10 py-1 px-3 rounded-full w-fit mx-auto mb-4">
            <BookOpen className="w-4 h-4 text-primary" />
            <span className="text-sm font-medium text-primary">
              Our Resources
            </span>
          </div>
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            Comprehensive Learning Materials
          </h2>
          <p className="text-gray-700 max-w-3xl mx-auto md:text-lg">
            We've developed quality resources aligned with our SDG focus areas
            to support your technology education journey.
          </p>
        </div>

        <div className="grid lg:grid-cols-3 gap-12">
          {/* Left Column - Categories */}
          <div className="lg:col-span-1">
            <h3 className="text-xl font-semibold mb-6 px-4">
              Resource Categories
            </h3>
            <div className="space-y-3">
              {resourceCategories.map((category) => (
                <button
                  key={category.id}
                  onClick={() => setActiveCategory(category.id)}
                  className={`w-full flex items-center p-4 rounded-xl transition-all ${
                    activeCategory === category.id
                      ? `${category.color} ${category.borderColor} border shadow-sm`
                      : "hover:bg-gray-100 border border-transparent"
                  }`}
                >
                  <div
                    className={`${category.iconBgColor} rounded-lg p-3 mr-4`}
                  >
                    {category.icon}
                  </div>
                  <div className="text-left">
                    <h4 className="font-medium">{category.title}</h4>
                    <p className="text-sm text-gray-600 line-clamp-1">
                      {category.description}
                    </p>
                  </div>
                  {activeCategory === category.id && (
                    <ChevronRight className={`ml-auto ${category.textColor}`} />
                  )}
                </button>
              ))}
            </div>

            {/* Quick Stats */}
            <div className="mt-10 bg-white rounded-xl p-6 border shadow-sm">
              <h4 className="text-lg font-semibold mb-4">Resource Stats</h4>
              <div className="grid grid-cols-2 gap-4">
                <div className="text-center">
                  <p className="text-3xl font-bold text-primary">50+</p>
                  <p className="text-sm text-gray-600">Learning Guides</p>
                </div>
                <div className="text-center">
                  <p className="text-3xl font-bold text-primary">30+</p>
                  <p className="text-sm text-gray-600">Tutorials</p>
                </div>
                <div className="text-center">
                  <p className="text-3xl font-bold text-primary">15k+</p>
                  <p className="text-sm text-gray-600">Downloads</p>
                </div>
                <div className="text-center">
                  <p className="text-3xl font-bold text-primary">4</p>
                  <p className="text-sm text-gray-600">SDGs Supported</p>
                </div>
              </div>
            </div>
          </div>

          {/* Right Column - Resource Items */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-xl p-6 border shadow-sm">
              {/* Category Header */}
              <div className="flex items-center gap-4 mb-8 pb-4 border-b">
                <div
                  className={`${currentCategory.iconBgColor} p-3 rounded-full`}
                >
                  {currentCategory.icon}
                </div>
                <div className="flex-grow">
                  <div className="flex items-center gap-3">
                    <h3 className="text-2xl font-bold">
                      {currentCategory.title}
                    </h3>
                    <span
                      className={`${currentCategory.color} ${currentCategory.textColor} text-xs px-2 py-1 rounded-full`}
                    >
                      {currentCategory.badgeText}
                    </span>
                  </div>
                  <p className="text-gray-600">{currentCategory.description}</p>
                </div>
              </div>

              {/* Resource Items Grid */}
              <div className="grid md:grid-cols-2 gap-6">
                {currentCategory.items.map((item, index) => (
                  <div
                    key={index}
                    className="group rounded-xl border overflow-hidden transition-all hover:shadow-md"
                  >
                    <div className="relative h-44">
                      <Image
                        src={item.image}
                        alt={item.title}
                        fill
                        className="object-cover group-hover:scale-105 transition-transform duration-300"
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-transparent"></div>
                      <div className="absolute bottom-4 left-4 right-4">
                        <h4 className="text-white font-bold text-lg">
                          {item.title}
                        </h4>
                      </div>
                    </div>
                    <div className="p-4">
                      <p className="text-gray-700 text-sm mb-4">
                        {item.description}
                      </p>
                      <Link
                        href={item.link}
                        className={`flex items-center text-sm font-medium ${currentCategory.textColor} gap-1 group-hover:underline`}
                      >
                        Access Resource
                        <ChevronRight className="w-4 h-4 group-hover:translate-x-0.5 transition-transform" />
                      </Link>
                    </div>
                  </div>
                ))}
              </div>

              {/* View All Button */}
              <div className="mt-8 text-center">
                <Button
                  className={`${currentCategory.buttonBg} text-white hover:opacity-90`}
                >
                  <Link
                    href={`/resources/${currentCategory.id}`}
                    className="flex items-center gap-2"
                  >
                    <span>View All {currentCategory.title}</span>
                    <ChevronRight className="h-4 w-4" />
                  </Link>
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
