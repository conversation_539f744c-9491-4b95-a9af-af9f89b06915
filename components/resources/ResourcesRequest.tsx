
import Link from "next/link";
import { Button } from "@/components/ui/button";

export default function ResourcesRequest() {
    return (
        <div className="w-full py-16 px-4 md:px-6 lg:px-8 bg-gray-50">
            <div className="max-w-4xl mx-auto">
                <div className="bg-white rounded-2xl shadow-sm border border-gray-100 p-8">
                    <div className="text-center mb-8">
                        <div className="bg-primary/10 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-6">
                            <svg
                                xmlns="http://www.w3.org/2000/svg"
                                width="24"
                                height="24"
                                viewBox="0 0 24 24"
                                fill="none"
                                stroke="currentColor"
                                strokeWidth="2"
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                className="text-primary"
                            >
                                <path d="M21 11.5a8.38 8.38 0 0 1-.9 3.8 8.5 8.5 0 0 1-7.6 4.7 8.38 8.38 0 0 1-3.8-.9L3 21l1.9-5.7a8.38 8.38 0 0 1-.9-3.8 8.5 8.5 0 0 1 4.7-7.6 8.38 8.38 0 0 1 3.8-.9h.5a8.48 8.48 0 0 1 8 8v.5z"></path>
                            </svg>
                        </div>
                        <h2 className="font-bold text-3xl mb-4">
                            Need Additional Resources?
                        </h2>
                        <p className="text-gray-600 max-w-lg mx-auto">
                            Can{"'"}t find what you{"'"}re looking for? Have a specific resource
                            need for your classroom, organization, or personal learning? Let
                            us know!
                        </p>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <h3 className="font-bold mb-4">Who we serve:</h3>
                            <ul className="space-y-2">
                                <li className="flex items-start gap-3">
                                    <div className="flex-shrink-0 w-6 h-6 bg-primary/10 rounded-full flex items-center justify-center mt-0.5">
                                        <div className="w-2 h-2 bg-primary rounded-full"></div>
                                    </div>
                                    <p className="text-gray-700">Educators & schools</p>
                                </li>
                                <li className="flex items-start gap-3">
                                    <div className="flex-shrink-0 w-6 h-6 bg-primary/10 rounded-full flex items-center justify-center mt-0.5">
                                        <div className="w-2 h-2 bg-primary rounded-full"></div>
                                    </div>
                                    <p className="text-gray-700">Community organizations</p>
                                </li>
                                <li className="flex items-start gap-3">
                                    <div className="flex-shrink-0 w-6 h-6 bg-primary/10 rounded-full flex items-center justify-center mt-0.5">
                                        <div className="w-2 h-2 bg-primary rounded-full"></div>
                                    </div>
                                    <p className="text-gray-700">Individual learners</p>
                                </li>
                                <li className="flex items-start gap-3">
                                    <div className="flex-shrink-0 w-6 h-6 bg-primary/10 rounded-full flex items-center justify-center mt-0.5">
                                        <div className="w-2 h-2 bg-primary rounded-full"></div>
                                    </div>
                                    <p className="text-gray-700">Parents & guardians</p>
                                </li>
                            </ul>
                        </div>

                        <div>
                            <h3 className="font-bold mb-4">
                                Types of resources available:
                            </h3>
                            <ul className="space-y-2">
                                <li className="flex items-start gap-3">
                                    <div className="flex-shrink-0 w-6 h-6 bg-primary/10 rounded-full flex items-center justify-center mt-0.5">
                                        <div className="w-2 h-2 bg-primary rounded-full"></div>
                                    </div>
                                    <p className="text-gray-700">Curriculum packages</p>
                                </li>
                                <li className="flex items-start gap-3">
                                    <div className="flex-shrink-0 w-6 h-6 bg-primary/10 rounded-full flex items-center justify-center mt-0.5">
                                        <div className="w-2 h-2 bg-primary rounded-full"></div>
                                    </div>
                                    <p className="text-gray-700">Printed handbooks & guides</p>
                                </li>
                                <li className="flex items-start gap-3">
                                    <div className="flex-shrink-0 w-6 h-6 bg-primary/10 rounded-full flex items-center justify-center mt-0.5">
                                        <div className="w-2 h-2 bg-primary rounded-full"></div>
                                    </div>
                                    <p className="text-gray-700">Workshop materials</p>
                                </li>
                                <li className="flex items-start gap-3">
                                    <div className="flex-shrink-0 w-6 h-6 bg-primary/10 rounded-full flex items-center justify-center mt-0.5">
                                        <div className="w-2 h-2 bg-primary rounded-full"></div>
                                    </div>
                                    <p className="text-gray-700">Digital learning tools</p>
                                </li>
                            </ul>
                        </div>
                    </div>

                    <div className="mt-8 text-center">
                        <Button className="bg-primary hover:bg-primary/90 text-white px-8 py-4">
                            <Link href="/contact">Request Resources</Link>
                        </Button>
                    </div>
                </div>
            </div>
        </div>


  )
}





