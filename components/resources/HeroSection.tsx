import Link from "next/link";
import { Button } from "@/components/ui/button";
import {
  ChevronRight,
  FileText,
  Download,
  BookO<PERSON>,
  Code,
  Printer,
  Heart,
} from "lucide-react";
import UniversalHeroSection from "@/components/ui/hero-section";

export default function HeroSection() {
  return (
    <div className="mt-16">
      <UniversalHeroSection
        imageUrl="/images/3d-pin.png"
        height="h-2/3"
        overlay={true}
        overlayOpacity={60}
        className="bg-gray-900 relative overflow-hidden"
      >
        {/* Enhanced accents */}
        <div className="absolute top-0 right-0 w-64 h-64 rounded-full bg-primary/10 blur-3xl opacity-50 -translate-y-1/2 translate-x-1/2"></div>
        <div className="absolute bottom-0 left-0 w-full h-32 bg-gradient-to-t from-gray-950/80 to-transparent pointer-events-none"></div>

        {/* Content with improved spacing and typography */}
        <div className="relative z-20 mx-auto max-w-7xl px-6 lg:px-8 h-full flex items-center">
          <div className="grid md:grid-cols-2 gap-12 items-center w-full">
            {/* Left side - Main content */}
            <div className="flex flex-col items-start space-y-6">
              <div className="inline-flex items-center gap-2 bg-primary/20 backdrop-blur-sm py-2 px-4 rounded-full border border-primary/30">
                <FileText className="w-4 h-4 text-primary" />
                <span className="text-sm font-medium text-primary">
                  Educational Resources
                </span>
              </div>

              <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold tracking-tight text-white leading-tight">
                Tools for Your{" "}
                <span className="text-primary bg-primary/5 px-2 rounded backdrop-blur-sm">
                  Tech Journey
                </span>
              </h1>

              <p className="text-xl text-gray-300 max-w-lg leading-relaxed">
                Discover a world of resources designed to empower your learning,
                creativity, and personal growth in technology.
              </p>

              <div className="flex flex-col sm:flex-row gap-4">
                <Button className="bg-primary hover:bg-primary/90 text-white px-8 py-3 text-lg">
                  <Link href="#resources" className="flex items-center gap-2">
                    <BookOpen className="w-5 h-5" />
                    <span>Explore Resources</span>
                  </Link>
                </Button>
                <Button
                  variant="outline"
                  className="border-white text-white hover:bg-white hover:text-gray-900 px-8 py-3 text-lg backdrop-blur-sm"
                >
                  <Link
                    href="/get-involved"
                    className="flex items-center gap-2"
                  >
                    <Download className="w-5 h-5" />
                    <span>Request Materials</span>
                  </Link>
                </Button>
              </div>
            </div>

            {/* Right side - Resource categories */}
            <div className="hidden md:block">
              <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-6">
                <h3 className="text-white text-xl font-semibold mb-6">
                  Popular Categories
                </h3>

                <div className="space-y-4">
                  {/* Category 1 */}
                  <div className="flex items-center p-3 hover:bg-white/10 rounded-lg transition-colors cursor-pointer group">
                    <div className="bg-blue-100 p-3 rounded-lg mr-4">
                      <Code className="w-5 h-5 text-blue-600" />
                    </div>
                    <div className="flex-grow">
                      <h4 className="text-white font-medium">Coding Guides</h4>
                      <p className="text-sm text-gray-300">
                        Tutorials, references & examples
                      </p>
                    </div>
                    <ChevronRight className="w-5 h-5 text-gray-400 group-hover:text-primary transition-colors" />
                  </div>

                  {/* Category 2 */}
                  <div className="flex items-center p-3 hover:bg-white/10 rounded-lg transition-colors cursor-pointer group">
                    <div className="bg-green-100 p-3 rounded-lg mr-4">
                      <Printer className="w-5 h-5 text-green-600" />
                    </div>
                    <div className="flex-grow">
                      <h4 className="text-white font-medium">3D Printing</h4>
                      <p className="text-sm text-gray-300">
                        Design files & tutorials
                      </p>
                    </div>
                    <ChevronRight className="w-5 h-5 text-gray-400 group-hover:text-primary transition-colors" />
                  </div>

                  {/* Category 3 */}
                  <div className="flex items-center p-3 hover:bg-white/10 rounded-lg transition-colors cursor-pointer group">
                    <div className="bg-pink-100 p-3 rounded-lg mr-4">
                      <Heart className="w-5 h-5 text-pink-600" />
                    </div>
                    <div className="flex-grow">
                      <h4 className="text-white font-medium">
                        Self-Help Guides
                      </h4>
                      <p className="text-sm text-gray-300">
                        Mental health & wellness
                      </p>
                    </div>
                    <ChevronRight className="w-5 h-5 text-gray-400 group-hover:text-primary transition-colors" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </UniversalHeroSection>
    </div>
  );
}
