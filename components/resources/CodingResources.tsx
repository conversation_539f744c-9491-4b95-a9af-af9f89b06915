"use client";

import Image from "next/image";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import UniversalCard from "../ui/universal-card";

export default function CodingResources() {
  const codingResources = [
    {
      id: "learn-to-code",
      title: "Learn to Code",
      description: "Interactive platforms for coding beginners",
      image: "/images/education-image.png",
      link: "/resources/learn-to-code",
      category: "Coding",
      color: "bg-blue-600",
      bgColor: "bg-blue-50",
    },
    {
      id: "essential-books",
      title: "Essential Books",
      description: "Curated coding guides and references",
      image: "/images/certificate-image.jpg",
      link: "/resources/coding-books",
      category: "Coding",
      color: "bg-blue-600",
      bgColor: "bg-blue-50",
    },
    {
      id: "coding-challenges",
      title: "Coding Challenges",
      description: "Inclusive beginner-friendly challenges",
      image: "/images/empowering-image.JPG",
      link: "/resources/coding-challenges",
      category: "Coding",
      color: "bg-blue-600",
      bgColor: "bg-blue-50",
    },
    {
      id: "coding-community",
      title: "Community Network",
      description: "Join our supportive coding community",
      image: "/images/inspiring-change.jpg",
      link: "/resources/coding-community",
      category: "Coding",
      color: "bg-blue-600",
      bgColor: "bg-blue-50",
    },
  ];

  return (
    <div
      id="coding-section"
      className="w-full py-20 px-5 md:px-10 lg:px-16 bg-white"
    >
      <div className="max-w-7xl mx-auto">
        <div className="flex flex-col md:flex-row items-start justify-between gap-8 mb-12">
          <div>
            <div className="flex items-center gap-3 mb-4">
              <div className="bg-blue-100 p-3 rounded-full">
                <Image
                  src="/vectors/github.svg"
                  alt="Coding resources"
                  height={24}
                  width={24}
                  className="w-6 h-6"
                  onError={(e) => {
                    e.currentTarget.src = "/vectors/github.svg";
                  }}
                />
              </div>
              <span className="text-sm font-medium text-blue-600">
                Coding Resources
              </span>
            </div>
            <h2 className="font-bold text-3xl xl:text-4xl leading-tight mb-2">
              Explore Our Comprehensive Coding Resources
            </h2>
          </div>
          <p className="text-base md:text-lg text-gray-700 max-w-xl">
            We've curated a collection of resources to support your coding
            journey, from beginner tutorials to advanced learning platforms.
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
          {codingResources.map((resource) => (
            <UniversalCard
              key={resource.id}
              type="resource"
              data={resource}
              layout="grid"
              showImage={true}
              showMeta={true}
            />
          ))}
        </div>
      </div>
    </div>
  );
}
