"use client";

import { blogPosts } from "@/data/blogData";

// Get all unique tags from blog posts
const allTags = [...new Set(blogPosts.flatMap((post) => post.tags))];

export default function PopularTags() {
  return (
    <div className="mb-16">
      <h2 className="text-2xl font-bold mb-6 flex items-center">
        <div className="bg-primary/10 p-2 rounded-full mr-3">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="20"
            height="20"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
            className="text-primary"
          >
            <path d="M20.59 13.41l-7.17 7.17a2 2 0 0 1-2.83 0L2 12V2h10l8.59 8.59a2 2 0 0 1 0 2.82z"></path>
            <line x1="7" y1="7" x2="7.01" y2="7"></line>
          </svg>
        </div>
        Popular Tags
      </h2>
      <div className="flex flex-wrap gap-3">
        {allTags.map((tag) => (
          <button
            key={tag}
            className="bg-gray-50 hover:bg-gray-100 text-gray-800 px-4 py-2 rounded-lg text-sm transition-colors border border-gray-200"
          >
            {tag}
          </button>
        ))}
      </div>
    </div>
  );
}
