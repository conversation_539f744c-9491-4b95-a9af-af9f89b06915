"use client";

import { But<PERSON> } from "@/components/ui/button";

export default function NewsletterSignup() {
    return (
        <div className="w-full py-20 px-5 md:px-10 lg:px-16 bg-gray-50">
            <div className="max-w-3xl mx-auto text-center">
                <div className="bg-white rounded-2xl shadow-sm border border-gray-100 p-8 md:p-12">
                    {/* Decorative element */}
                    <div className="bg-primary/10 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-6">
                        <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="24"
                            height="24"
                            viewBox="0 0 24 24"
                            fill="none"
                            stroke="currentColor"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            className="text-primary"
                        >
                            <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path>
                            <polyline points="22,6 12,13 2,6"></polyline>
                        </svg>
                    </div>

                    <h2 className="font-bold text-3xl md:text-4xl mb-4">
                        Stay Updated
                    </h2>

                    <p className="text-base md:text-lg text-gray-600 mb-8 max-w-lg mx-auto">
                        Subscribe to our newsletter for the latest updates from GirlCode{"'"}s
                        initiatives.
                    </p>

                    <div className="flex flex-col md:flex-row gap-3 max-w-lg mx-auto">
                        <input
                            type="email"
                            placeholder="Your email address"
                            className="flex-grow p-4 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary"
                        />
                        <Button className="bg-primary hover:bg-primary/90 md:w-auto py-4 px-8 rounded-lg">
                            Subscribe
                        </Button>
                    </div>

                    <div className="mt-6 flex items-center justify-center">
                        <input type="checkbox" id="blog-updates" className="mr-2" />
                        <label htmlFor="blog-updates" className="text-sm text-gray-600">
                            I{"'"}m interested in receiving new blog posts and updates
                        </label>
                    </div>
                </div>
            </div>
        </div>

    )
}
