"use client";

import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Search, Filter, X, Calendar, Tag, Target } from "lucide-react";
import { BlogPost } from "@/data/blogData";

interface FilterSidebarProps {
  posts: BlogPost[];
  onFilterChange: (filteredPosts: BlogPost[]) => void;
  isOpen: boolean;
  onToggle: () => void;
}

export default function FilterSidebar({
  posts,
  onFilterChange,
  isOpen,
  onToggle,
}: FilterSidebarProps) {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategories, setSelectedCategories] = useState<string[]>([]);
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [selectedSDGs, setSelectedSDGs] = useState<number[]>([]);
  const [dateRange, setDateRange] = useState<string>("all");

  // Extract unique values from posts
  const categories = Array.from(new Set(posts.map((post) => post.category)));
  const allTags = Array.from(new Set(posts.flatMap((post) => post.tags)));
  const allSDGs = Array.from(
    new Set(posts.flatMap((post) => post.relatedSDGs))
  ).sort((a, b) => a - b);

  const applyFilters = () => {
    let filtered = posts;

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(
        (post) =>
          post.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
          post.excerpt.toLowerCase().includes(searchTerm.toLowerCase()) ||
          post.author.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Category filter
    if (selectedCategories.length > 0) {
      filtered = filtered.filter((post) =>
        selectedCategories.includes(post.category)
      );
    }

    // Tags filter
    if (selectedTags.length > 0) {
      filtered = filtered.filter((post) =>
        post.tags.some((tag) => selectedTags.includes(tag))
      );
    }

    // SDG filter
    if (selectedSDGs.length > 0) {
      filtered = filtered.filter((post) =>
        post.relatedSDGs.some((sdg) => selectedSDGs.includes(sdg))
      );
    }

    // Date filter
    if (dateRange !== "all") {
      const now = new Date();
      const filterDate = new Date();

      switch (dateRange) {
        case "week":
          filterDate.setDate(now.getDate() - 7);
          break;
        case "month":
          filterDate.setMonth(now.getMonth() - 1);
          break;
        case "quarter":
          filterDate.setMonth(now.getMonth() - 3);
          break;
        case "year":
          filterDate.setFullYear(now.getFullYear() - 1);
          break;
      }

      filtered = filtered.filter((post) => {
        const postDate = new Date(post.date);
        return postDate >= filterDate;
      });
    }

    onFilterChange(filtered);
  };

  const clearAllFilters = () => {
    setSearchTerm("");
    setSelectedCategories([]);
    setSelectedTags([]);
    setSelectedSDGs([]);
    setDateRange("all");
    onFilterChange(posts);
  };

  const toggleCategory = (category: string) => {
    setSelectedCategories((prev) =>
      prev.includes(category)
        ? prev.filter((c) => c !== category)
        : [...prev, category]
    );
  };

  const toggleTag = (tag: string) => {
    setSelectedTags((prev) =>
      prev.includes(tag) ? prev.filter((t) => t !== tag) : [...prev, tag]
    );
  };

  const toggleSDG = (sdg: number) => {
    setSelectedSDGs((prev) =>
      prev.includes(sdg) ? prev.filter((s) => s !== sdg) : [...prev, sdg]
    );
  };

  // Apply filters whenever any filter state changes
  React.useEffect(() => {
    applyFilters();
  }, [searchTerm, selectedCategories, selectedTags, selectedSDGs, dateRange]);

  const activeFiltersCount =
    selectedCategories.length +
    selectedTags.length +
    selectedSDGs.length +
    (searchTerm ? 1 : 0) +
    (dateRange !== "all" ? 1 : 0);

  return (
    <>
      {/* Mobile filter toggle */}
      <div className="lg:hidden mb-6">
        <Button
          onClick={onToggle}
          variant="outline"
          className="w-full flex items-center justify-center gap-2"
        >
          <Filter className="h-4 w-4" />
          Filters
          {activeFiltersCount > 0 && (
            <Badge variant="secondary" className="ml-2">
              {activeFiltersCount}
            </Badge>
          )}
        </Button>
      </div>

      {/* Filter sidebar */}
      <div
        className={`
        lg:block ${isOpen ? "block" : "hidden"} 
        bg-white border border-gray-200 rounded-lg p-6 space-y-6
        lg:sticky lg:top-6 lg:h-fit
      `}
      >
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Filters
          </h3>
          {activeFiltersCount > 0 && (
            <Button
              onClick={clearAllFilters}
              variant="ghost"
              size="sm"
              className="text-red-600 hover:text-red-700"
            >
              Clear All
            </Button>
          )}
        </div>

        {/* Search */}
        <div className="space-y-2">
          <label className="text-sm font-medium flex items-center gap-2">
            <Search className="h-4 w-4" />
            Search Articles
          </label>
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search by title, content, or author..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
            />
          </div>
        </div>

        {/* Date Range */}
        <div className="space-y-2">
          <label className="text-sm font-medium flex items-center gap-2">
            <Calendar className="h-4 w-4" />
            Date Range
          </label>
          <select
            value={dateRange}
            onChange={(e) => setDateRange(e.target.value)}
            className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
          >
            <option value="all">All Time</option>
            <option value="week">Past Week</option>
            <option value="month">Past Month</option>
            <option value="quarter">Past 3 Months</option>
            <option value="year">Past Year</option>
          </select>
        </div>

        {/* Categories */}
        <div className="space-y-2">
          <label className="text-sm font-medium">Categories</label>
          <div className="space-y-1">
            {categories.map((category) => (
              <label
                key={category}
                className="flex items-center space-x-2 cursor-pointer"
              >
                <input
                  type="checkbox"
                  checked={selectedCategories.includes(category)}
                  onChange={() => toggleCategory(category)}
                  className="rounded border-gray-300 text-primary focus:ring-primary"
                />
                <span className="text-sm">{category}</span>
                <span className="text-xs text-gray-500">
                  ({posts.filter((p) => p.category === category).length})
                </span>
              </label>
            ))}
          </div>
        </div>

        {/* Tags */}
        <div className="space-y-2">
          <label className="text-sm font-medium flex items-center gap-2">
            <Tag className="h-4 w-4" />
            Tags
          </label>
          <div className="flex flex-wrap gap-2">
            {allTags.map((tag) => (
              <Badge
                key={tag}
                variant={selectedTags.includes(tag) ? "default" : "outline"}
                className="cursor-pointer hover:bg-primary/10"
                onClick={() => toggleTag(tag)}
              >
                {tag}
                {selectedTags.includes(tag) && <X className="h-3 w-3 ml-1" />}
              </Badge>
            ))}
          </div>
        </div>

        {/* SDGs */}
        <div className="space-y-2">
          <label className="text-sm font-medium flex items-center gap-2">
            <Target className="h-4 w-4" />
            UN SDG Goals
          </label>
          <div className="grid grid-cols-3 gap-2">
            {allSDGs.map((sdg) => (
              <Badge
                key={sdg}
                variant={selectedSDGs.includes(sdg) ? "default" : "outline"}
                className="cursor-pointer hover:bg-primary/10 justify-center"
                onClick={() => toggleSDG(sdg)}
              >
                SDG {sdg}
                {selectedSDGs.includes(sdg) && <X className="h-3 w-3 ml-1" />}
              </Badge>
            ))}
          </div>
        </div>

        {/* Active filters summary */}
        {activeFiltersCount > 0 && (
          <div className="pt-4 border-t">
            <div className="text-sm text-gray-600">
              Showing filtered results ({activeFiltersCount} filter
              {activeFiltersCount !== 1 ? "s" : ""} active)
            </div>
          </div>
        )}
      </div>
    </>
  );
}
