"use client";

import { But<PERSON> } from "@/components/ui/button";
import { useState } from "react";
import { ChevronLeft, ChevronRight, MoreHorizontal } from "lucide-react";

interface PaginationProps {
  totalPages?: number;
  currentPage?: number;
  onPageChange?: (page: number) => void;
  maxDisplayed?: number;
}

export default function Pagination({
  totalPages = 10,
  currentPage = 1,
  onPageChange = () => {},
  maxDisplayed = 5,
}: PaginationProps) {
  // Calculate which page numbers to display
  const getPageNumbers = () => {
    // If we have fewer pages than the max to display, show all pages
    if (totalPages <= maxDisplayed) {
      return Array.from({ length: totalPages }, (_, i) => i + 1);
    }

    // Calculate how many pages to show on each side of the current page
    const sidePages = Math.floor((maxDisplayed - 3) / 2); // -3 for current, first, and last pages

    let startPage = Math.max(2, currentPage - sidePages);
    let endPage = Math.min(totalPages - 1, currentPage + sidePages);

    // Adjust if we're near the beginning or end
    if (currentPage <= sidePages + 1) {
      endPage = maxDisplayed - 1;
    } else if (currentPage >= totalPages - sidePages) {
      startPage = totalPages - maxDisplayed + 2;
    }

    // Build the pages array
    const pages = [1]; // Always include first page

    // Add ellipsis after first page if needed
    if (startPage > 2) {
      pages.push(-1); // Use negative number to represent ellipsis
    }

    // Add middle pages
    for (let i = startPage; i <= endPage; i++) {
      pages.push(i);
    }

    // Add ellipsis before last page if needed
    if (endPage < totalPages - 1) {
      pages.push(-2); // Use different negative number for second ellipsis
    }

    // Always include last page if not already included
    if (totalPages > 1) {
      pages.push(totalPages);
    }

    return pages;
  };

  const pageNumbers = getPageNumbers();

  const handlePreviousPage = () => {
    if (currentPage > 1) {
      onPageChange(currentPage - 1);
    }
  };

  const handleNextPage = () => {
    if (currentPage < totalPages) {
      onPageChange(currentPage + 1);
    }
  };

  return (
    <div className="mt-12 flex justify-center">
      <div className="flex flex-wrap items-center gap-2">
        {/* Previous button */}
        <Button
          variant="outline"
          className="flex items-center gap-1 text-sm"
          onClick={handlePreviousPage}
          disabled={currentPage === 1}
        >
          <ChevronLeft className="w-4 h-4" />
          <span className="hidden sm:inline">Previous</span>
        </Button>

        {/* Page numbers */}
        <div className="flex items-center gap-1.5">
          {pageNumbers.map((pageNumber, index) => {
            // Render ellipsis
            if (pageNumber < 0) {
              return (
                <span
                  key={`ellipsis-${index}`}
                  className="w-9 h-9 flex items-center justify-center text-gray-400"
                >
                  <MoreHorizontal className="w-4 h-4" />
                </span>
              );
            }

            // Render page number button
            return (
              <Button
                key={`page-${pageNumber}`}
                variant={currentPage === pageNumber ? "default" : "outline"}
                className={`w-9 h-9 p-0 font-medium text-sm ${
                  currentPage === pageNumber
                    ? "bg-primary hover:bg-primary/90 text-white"
                    : "hover:bg-gray-50"
                }`}
                onClick={() => onPageChange(pageNumber)}
              >
                {pageNumber}
              </Button>
            );
          })}
        </div>

        {/* Next button */}
        <Button
          variant="outline"
          className="flex items-center gap-1 text-sm"
          onClick={handleNextPage}
          disabled={currentPage === totalPages}
        >
          <span className="hidden sm:inline">Next</span>
          <ChevronRight className="w-4 h-4" />
        </Button>
      </div>
    </div>
  );
}
