"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowR<PERSON>, Clock3 } from "lucide-react";
import { useState } from "react";
import { blogPosts, BlogPost } from "@/data/blogData";
import UniversalCard from "../ui/universal-card";

export default function LatestPostSection() {
  const regularPosts = blogPosts.filter((post) => !post.featured);
  const [viewLayout, setViewLayout] = useState<"grid" | "list">("grid");

  // Get the first post for the featured latest post
  const featuredLatestPost = regularPosts[0];
  // Get the rest of the posts for the grid
  const remainingPosts = regularPosts.slice(1);

  return (
    <div className="container mx-auto max-w-7xl px-4 py-12">
      <div className="flex justify-between items-center mb-8">
        <div className="flex items-center gap-3">
          <div className="bg-primary/10 p-2 rounded-full">
            <Clock3 className="text-primary w-5 h-5" />
          </div>
          <h2 className="text-2xl font-bold">Latest Articles</h2>
        </div>

        <div className="flex gap-3 items-center">
          {/* View toggle buttons */}
          <div className="hidden sm:flex bg-gray-100 rounded-lg p-1">
            <button
              onClick={() => setViewLayout("grid")}
              className={`p-1.5 rounded ${
                viewLayout === "grid"
                  ? "bg-white shadow-sm"
                  : "text-gray-500 hover:text-gray-700"
              }`}
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="18"
                height="18"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <rect x="3" y="3" width="7" height="7"></rect>
                <rect x="14" y="3" width="7" height="7"></rect>
                <rect x="14" y="14" width="7" height="7"></rect>
                <rect x="3" y="14" width="7" height="7"></rect>
              </svg>
            </button>
            <button
              onClick={() => setViewLayout("list")}
              className={`p-1.5 rounded ${
                viewLayout === "list"
                  ? "bg-white shadow-sm"
                  : "text-gray-500 hover:text-gray-700"
              }`}
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="18"
                height="18"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <line x1="8" y1="6" x2="21" y2="6"></line>
                <line x1="8" y1="12" x2="21" y2="12"></line>
                <line x1="8" y1="18" x2="21" y2="18"></line>
                <line x1="3" y1="6" x2="3.01" y2="6"></line>
                <line x1="3" y1="12" x2="3.01" y2="12"></line>
                <line x1="3" y1="18" x2="3.01" y2="18"></line>
              </svg>
            </button>
          </div>
        </div>
      </div>

      {/* Featured latest post - always in list view */}
      {featuredLatestPost && (
        <div className="mb-8">
          <UniversalCard
            type="blog"
            data={{
              id: featuredLatestPost.id,
              title: featuredLatestPost.title,
              excerpt: featuredLatestPost.excerpt,
              image: featuredLatestPost.coverImage,
              category: featuredLatestPost.category,
              author: featuredLatestPost.author,
              authorImage: featuredLatestPost.authorImage,
              date: featuredLatestPost.date,
              readTime: featuredLatestPost.readTime,
              tags: featuredLatestPost.tags,
              color: featuredLatestPost.color,
              bgColor: featuredLatestPost.lightColor,
            }}
            layout="list"
            showImage={true}
            showMeta={true}
          />
        </div>
      )}

      {/* Remaining posts in grid or list layout */}
      <div
        className={
          viewLayout === "grid"
            ? "grid md:grid-cols-2 lg:grid-cols-3 gap-6"
            : "space-y-6"
        }
      >
        {remainingPosts.map((post) => (
          <UniversalCard
            key={post.id}
            type="blog"
            data={{
              id: post.id,
              title: post.title,
              excerpt: post.excerpt,
              image: post.coverImage,
              category: post.category,
              author: post.author,
              authorImage: post.authorImage,
              date: post.date,
              readTime: post.readTime,
              tags: post.tags,
              color: post.color,
              bgColor: post.lightColor,
            }}
            layout={viewLayout}
            showImage={true}
            showMeta={true}
          />
        ))}
      </div>

      {/* "Load More" button */}
      <div className="mt-10 text-center">
        <button className="inline-flex items-center gap-2 bg-white border border-gray-200 px-6 py-3 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors">
          <span>Load More Articles</span>
          <ArrowRight className="h-4 w-4" />
        </button>
      </div>
    </div>
  );
}
