"use client";

import Image from "next/image";
import { But<PERSON> } from "@/components/ui/button";
import { Search, ChevronDown, ChevronRight } from "lucide-react";
import { useState } from "react";
import HeroSection from "@/components/ui/hero-section";
import { blogPosts, categories, allTags } from "@/data/blogData";
import Link from "next/link";

export default function BlogHeader() {
  const [selectedCategory, setSelectedCategory] = useState("All");
  const [searchQuery, setSearchQuery] = useState("");

  // Featured post for header background
  const featuredPost = blogPosts.find((post) => post.featured) || blogPosts[0];
  return (
    <div className="mt-16">
      {/* Hero Section - Enhanced */}
      <HeroSection
        imageUrl={featuredPost.coverImage}
        height="h-2/3"
        overlay={true}
        overlayOpacity={60}
        className="bg-gray-900"
      >
        {/* Enhanced accents */}
        <div className="absolute -top-24 -right-24 w-64 h-64 rounded-full bg-primary/10 blur-3xl"></div>
        <div className="absolute bottom-0 left-0 w-full h-32 bg-gradient-to-t from-gray-950 to-transparent"></div>

        {/* Content with improved spacing and typography */}
        <div className="relative z-10 mx-auto max-w-7xl px-6 lg:px-8">
          <div className="grid md:grid-cols-2 gap-12 items-start">
            {/* Left side - Main content */}
            <div className="flex flex-col items-start mb-12 md:mb-0">
              <div className="inline-flex items-center gap-2 bg-primary/10 py-1.5 px-4 rounded-full mb-6 ">
                <span className="text-sm font-medium text-primary">
                  Our Blog
                </span>
              </div>

              <h1 className="text-4xl text-left font-bold tracking-tight text-white sm:text-5xl md:text-6xl leading-tight mb-6">
                Insights, Stories &{" "}
                <span className="text-primary bg-primary/5 px-2 rounded">
                  Updates
                </span>
              </h1>

              <p className="text-lg text-left text-gray-300 mb-8 max-w-lg leading-relaxed">
                Explore our latest articles featuring success stories, program
                updates, and insights into how technology is transforming lives
                across our initiatives.
              </p>

              {/* Featured tags */}
              <div className="flex flex-wrap gap-2 mb-12">
                {allTags.slice(0, 5).map((tag) => (
                  <span
                    key={tag}
                    className="px-3 py-1.5 bg-white/10 backdrop-blur-sm text-white text-sm rounded-full hover:bg-white/20 transition-colors cursor-pointer"
                  >
                    {tag}
                  </span>
                ))}
                <span className="px-3 py-1.5 bg-white/5 backdrop-blur-sm text-white/70 text-sm rounded-full flex items-center">
                  <ChevronDown className="h-3.5 w-3.5 mr-1" />
                  More
                </span>
              </div>
            </div>

            {/* Right side - Featured post preview */}
            <div className="hidden md:block">
              <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl overflow-hidden hover:bg-white/10 transition-colors group">
                <div className="p-6">
                  <div className="mb-4">
                    <span
                      className={`text-xs font-medium px-2.5 py-1 rounded-full ${featuredPost.color} text-white`}
                    >
                      {featuredPost.category}
                    </span>
                  </div>
                  <h2 className="text-xl font-bold text-white mb-3 group-hover:text-primary transition-colors">
                    {featuredPost.title}
                  </h2>
                  <p className="text-gray-300 text-sm mb-4">
                    {featuredPost.excerpt}
                  </p>
                  <div className="flex justify-between items-center text-sm">
                    <div className="flex items-center">
                      {" "}
                      <div className="w-8 h-8 rounded-full overflow-hidden mr-2">
                        <Image
                          src={featuredPost.authorImage}
                          alt={`${featuredPost.author} - Author`}
                          width={32}
                          height={32}
                          className="w-full h-full object-cover"
                          quality={75}
                          loading="lazy"
                          placeholder="blur"
                          blurDataURL="data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAAIAAoDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAhEAACAQMDBQAAAAAAAAAAAAABAgMABAUGIWGRkrHB0f/EABUBAQEAAAAAAAAAAAAAAAAAAAMF/8QAGhEAAgIDAAAAAAAAAAAAAAAAAAECEgMRkf/aAAwDAQACEQMRAD8AltJagyeH0AthI5xdrLcNM91BF5pX2HaH9bcfaSXWGaRmknyJckliyjqTzSlT54b6bk+h0R//2Q=="
                        />
                      </div>
                      <span className="text-white">{featuredPost.author}</span>
                    </div>
                    <span className="text-gray-400">
                      {featuredPost.readTime}
                    </span>
                  </div>
                </div>
                <div className="px-6 py-3 border-t border-white/10 flex justify-between items-center">
                  <span className="text-gray-400 text-sm">
                    {featuredPost.date}
                  </span>{" "}
                  <Button className="bg-primary hover:bg-primary/90 text-white group">
                    <Link
                      href={`/blogs-and-news/${featuredPost.id}`}
                      className="flex items-center gap-1"
                    >
                      <span>Read Article</span>
                      <ChevronRight className="h-4 w-4 group-hover:translate-x-0.5 transition-transform" />
                    </Link>
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </HeroSection>
    </div>
  );
}
