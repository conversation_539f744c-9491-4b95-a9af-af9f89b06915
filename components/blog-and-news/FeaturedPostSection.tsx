"use client";

import { Star } from "lucide-react";
import { blogPosts } from "@/data/blogData";
import UniversalCard from "../ui/universal-card";

export default function FeaturedPostSection() {
  const featuredPosts = blogPosts.filter((post) => post.featured);

  return featuredPosts.length > 0 ? (
    <div className="container max-w-7xl mx-auto px-4 py-12">
      <div className="flex items-center justify-between mb-8">
        <div className="flex items-center gap-3">
          <div className="bg-primary/10 p-2 rounded-full">
            <Star className="text-primary w-5 h-5" />
          </div>
          <h2 className="text-2xl font-bold">Featured Articles</h2>
        </div>
      </div>

      <div className="grid grid-cols-1 gap-8">
        {/* Primary featured post (larger layout) */}
        {featuredPosts.length > 0 && (
          <UniversalCard
            type="blog"
            data={{
              id: featuredPosts[0].id,
              title: featuredPosts[0].title,
              excerpt: featuredPosts[0].excerpt,
              image: featuredPosts[0].coverImage,
              category: featuredPosts[0].category,
              author: featuredPosts[0].author,
              authorImage: featuredPosts[0].authorImage,
              date: featuredPosts[0].date,
              readTime: featuredPosts[0].readTime,
              tags: featuredPosts[0].tags,
              color: featuredPosts[0].color,
              bgColor: featuredPosts[0].lightColor,
            }}
            layout="featured"
            showImage={true}
            showMeta={true}
          />
        )}

        {/* Secondary featured posts (smaller cards in a grid) */}
        {featuredPosts.length > 1 && (
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            {featuredPosts.slice(1).map((post) => (
              <UniversalCard
                key={post.id}
                type="blog"
                data={{
                  id: post.id,
                  title: post.title,
                  excerpt: post.excerpt,
                  image: post.coverImage,
                  category: post.category,
                  author: post.author,
                  authorImage: post.authorImage,
                  date: post.date,
                  readTime: post.readTime,
                  tags: post.tags,
                  color: post.color,
                  bgColor: post.lightColor,
                }}
                layout="grid"
                showImage={true}
                showMeta={true}
              />
            ))}
          </div>
        )}
      </div>
    </div>
  ) : null;
}
