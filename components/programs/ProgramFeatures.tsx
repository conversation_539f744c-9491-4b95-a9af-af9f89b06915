"use client";

import { useState } from "react";
import Image from "next/image";
import Link from "next/link";
import { ChevronRight } from "lucide-react";

type Program = {
  title: string;
  description: string;
  link: string;
  highlights: string[];
};

type SDG = {
  id: number;
  title: string;
  description: string;
  impact: string;
  icon: string;
  image: string;
  color: string;
  bgColor: string;
  textColor: string;
  borderColor: string;
  programs: Program[];
};

const SDG_DATA: SDG[] = [
  {
    id: 4,
    title: "Quality Education",
    description:
      "Ensuring inclusive and equitable quality education and promoting lifelong learning opportunities for all. Our Tech Skills for Financial Freedom program equips Kenyan youth with cutting-edge, monetizable skills to thrive in the digital economy.",
    impact:
      "Over 1,500 girls have gained essential skills through our education initiatives",
    icon: "/vectors/books.svg",
    image: "/images/education-image.png",
    color: "#c5192d",
    bgColor: "bg-blue-50",
    borderColor: "border-blue-100",
    textColor: "text-blue-600",
    programs: [
      {
        title: "Tech Skills for Financial Freedom",
        description:
          "Empowering girls with essential tech and financial literacy skills to create economic opportunities.",
        link: "/programs/tech-financial-freedom",
        highlights: [
          "Digital literacy fundamentals",
          "Basic coding and web development",
          "Financial planning and entrepreneurship",
          "Personal branding and professional skills",
        ],
      },
      {
        title: "Coding & 3D Design Bootcamps",
        description:
          "Intensive technical training in coding and 3D design to unlock creative and professional potential.",
        link: "/programs/coding-bootcamps",
        highlights: [
          "Web development (HTML, CSS, JavaScript)",
          "3D modeling and design",
          "Prototyping and design thinking",
          "Portfolio development",
        ],
      },
      {
        title: "Financial Independence Workshops",
        description:
          "Empowering girls with financial knowledge and skills to build independent futures.",
        link: "/programs/financial-workshops",
        highlights: [
          "Personal financial management",
          "Investment basics",
          "Entrepreneurship fundamentals",
          "Digital economy opportunities",
        ],
      },
    ],
  },
  {
    id: 5,
    title: "Gender Equality",
    description:
      "Achieve gender equality and empower all women and girls through access to technology and digital skills. Our SGBV initiatives work directly with survivors to provide tech training, counseling, and advocacy.",
    impact:
      "Created safe learning environments for 3,000+ girls, with 78% reporting increased confidence in tech spaces",
    icon: "/vectors/rainbow.svg",
    image: "/images/empowering-image.JPG",
    color: "#ff3a21",
    bgColor: "bg-pink-50",
    borderColor: "border-pink-100",
    textColor: "text-pink-600",
    programs: [
      {
        title: "Ending SGBV, Empowering Survivors",
        description:
          "Comprehensive support program for survivors of sexual and gender-based violence.",
        link: "/programs/empowering-survivors",
        highlights: [
          "Counseling and mental health resources",
          "Digital skills training",
          "Economic empowerment programs",
          "Legal and support network connections",
        ],
      },
      {
        title: "Rescue Center Partnerships",
        description:
          "Collaborative program supporting rescue centers with technology and education.",
        link: "/programs/rescue-centers",
        highlights: [
          "Technology training for center staff",
          "Educational programs for survivors",
          "Digital skill-building workshops",
          "Economic independence support",
        ],
      },
      {
        title: "Advocacy in Action",
        description:
          "Active campaigns and initiatives to promote gender equality and women's rights.",
        link: "/programs/advocacy",
        highlights: [
          "Digital rights awareness",
          "Gender equality in tech",
          "Community education programs",
          "Policy advocacy initiatives",
        ],
      },
    ],
  },
  {
    id: 13,
    title: "Climate Action",
    description:
      "Take urgent action to combat climate change and its impacts through education and sustainable practices. We merge tech with eco-conscious practices through innovative green technologies and sustainable manufacturing.",
    impact:
      "Trained 1,200+ girls in sustainable design principles and eco-friendly manufacturing methods",
    icon: "/vectors/cloud-sun.svg",
    image: "/images/image-3.jpg",
    color: "#3f7e44",
    bgColor: "bg-green-50",
    borderColor: "border-green-100",
    textColor: "text-green-600",
    programs: [
      {
        title: "Sustainable Manufacturing, Responsible Innovation",
        description:
          "Educating girls on sustainable manufacturing and innovative green technologies.",
        link: "/programs/sustainable-manufacturing",
        highlights: [
          "Sustainable design principles",
          "Eco-friendly manufacturing techniques",
          "Circular economy concepts",
          "Green technology innovation",
        ],
      },
      {
        title: "Eco-Friendly 3D Printing",
        description:
          "Exploring sustainable 3D printing technologies and practices.",
        link: "/programs/eco-3d-printing",
        highlights: [
          "Sustainable 3D printing materials",
          "Eco-design principles",
          "Waste reduction techniques",
          "Green manufacturing processes",
        ],
      },
      {
        title: "Green Innovation Hubs",
        description:
          "Fostering innovation and creativity in sustainable technology solutions.",
        link: "/programs/green-innovation",
        highlights: [
          "Climate innovation workshops",
          "Sustainable technology design",
          "Environmental problem-solving",
          "Collaborative green tech projects",
        ],
      },
    ],
  },
  {
    id: 3,
    title: "Good Health & Well-Being",
    description:
      "Ensure healthy lives and promote well-being for all at all ages through technology and community support. Our mental health initiatives support holistic growth through resources, handbooks, and peer networks.",
    impact:
      "Distributed 15,000+ copies of the Teen Self-Help Handbook, reaching girls in 18 countries",
    icon: "/vectors/books.svg",
    image: "/images/inspiring-change.jpg",
    color: "#4c9f38",
    bgColor: "bg-purple-50",
    borderColor: "border-purple-100",
    textColor: "text-purple-600",
    programs: [
      {
        title: "Mental Health for Thriving Futures",
        description:
          "Comprehensive mental health support and education for girls.",
        link: "/programs/mental-health",
        highlights: [
          "Mental health awareness",
          "Coping skills workshops",
          "Peer support networks",
          "Digital wellness resources",
        ],
      },
      {
        title: "Teen Self-Help Handbook",
        description:
          "Comprehensive resource for teen mental health and personal development.",
        link: "/programs/self-help-handbook",
        highlights: [
          "Mental health fundamentals",
          "Self-care strategies",
          "Personal development exercises",
          "Resource directory",
        ],
      },
      {
        title: "Peer Support Networks",
        description:
          "Building supportive communities for mental health and personal growth.",
        link: "/programs/peer-support",
        highlights: [
          "Structured support groups",
          "Mentorship programs",
          "Skill-sharing workshops",
          "Community building",
        ],
      },
    ],
  },
];

// Program Features Data
const PROGRAM_FEATURES = [
  {
    title: "Hands-On Learning",
    description:
      "Every program incorporates practical, project-based learning where participants build real solutions to real problems.",
    icon: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="32"
        height="32"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
        className="text-blue-600"
      >
        <rect x="2" y="7" width="20" height="14" rx="2" ry="2"></rect>
        <path d="M16 21V5a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16"></path>
      </svg>
    ),
    highlights: [
      "Interactive workshops",
      "Built-in practice time",
      "Take-home projects",
    ],
    bgColor: "bg-blue-50",
    borderColor: "border-blue-100",
    iconBgColor: "bg-blue-100",
    dotColor: "bg-blue-600",
  },
  {
    title: "Mentorship & Support",
    description:
      "We pair every participant with mentors who provide guidance, support, and real-world industry insights.",
    icon: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="32"
        height="32"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
        className="text-pink-600"
      >
        <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
        <circle cx="9" cy="7" r="4"></circle>
        <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
        <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
      </svg>
    ),
    highlights: [
      "1:1 mentorship sessions",
      "Peer support groups",
      "Post-program follow-up",
    ],
    bgColor: "bg-pink-50",
    borderColor: "border-pink-100",
    iconBgColor: "bg-pink-100",
    dotColor: "bg-pink-600",
  },
  {
    title: "Holistic Approach",
    description:
      "We integrate personal development, mental health support, and life skills alongside technical training.",
    icon: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="32"
        height="32"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
        className="text-green-600"
      >
        <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
        <polyline points="22 4 12 14.01 9 11.01"></polyline>
      </svg>
    ),
    highlights: [
      "Wellness workshops",
      "Leadership development",
      "Communication skills",
    ],
    bgColor: "bg-green-50",
    borderColor: "border-green-100",
    iconBgColor: "bg-green-100",
    dotColor: "bg-green-600",
  },
];

// Program Card Component
const ProgramCard = ({
  program,
  color,
}: {
  program: Program;
  color: string;
}) => (
  <div className="group relative flex gap-x-6 rounded-lg p-4 hover:bg-gray-50 transition-all duration-200 border border-gray-100">
    <div className="mt-1 flex w-11 h-11 flex-none items-center justify-center rounded-lg bg-gray-50 group-hover:bg-white">
      <div
        className="w-6 h-6 text-gray-600 group-hover:text-primary"
        style={{ color }}
      >
        <ChevronRight className="w-6 h-6" />
      </div>
    </div>
    <div>
      <Link href={program.link} className="font-semibold text-gray-900">
        {program.title}
        <span className="absolute inset-0"></span>
      </Link>
      <p className="mt-1 text-sm text-gray-600">{program.description}</p>

      <div className="mt-3">
        <p className="text-xs font-medium text-gray-500 mb-1">
          Key Highlights:
        </p>
        <div className="flex flex-wrap gap-2">
          {program.highlights.slice(0, 3).map((highlight, index) => (
            <span
              key={index}
              className="text-xs px-2 py-1 rounded-full bg-gray-100 text-gray-700"
            >
              {highlight}
            </span>
          ))}
        </div>
      </div>
    </div>
  </div>
);

// Feature Card Component
const FeatureCard = ({ feature }: { feature: any }) => (
  <div
    className={`${feature.bgColor} rounded-xl p-6 border ${feature.borderColor}`}
  >
    <div
      className={`${feature.iconBgColor} rounded-full w-14 h-14 flex items-center justify-center mb-5`}
    >
      {feature.icon}
    </div>
    <h3 className="text-xl font-bold mb-3">{feature.title}</h3>
    <p className="text-gray-700 mb-5 text-sm">{feature.description}</p>
    <ul className="space-y-2">
      {feature.highlights.map((item: string, index: number) => (
        <li key={index} className="flex items-center gap-2">
          <div className={`w-1.5 h-1.5 rounded-full ${feature.dotColor}`}></div>
          <span className="text-sm">{item}</span>
        </li>
      ))}
    </ul>
  </div>
);

export default function ProgramsPage() {
  const [activeSDG, setActiveSDG] = useState<number>(4);
  const currentSDG =
    SDG_DATA.find((sdg) => sdg.id === activeSDG) || SDG_DATA[0];

  return (
    <div className="bg-white">
      {/* Hero Section */}
      <div className="relative bg-gradient-to-b from-gray-50 to-white pt-16 pb-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <div className="inline-flex items-center gap-2 bg-primary/10 py-1 px-3 rounded-full w-fit mx-auto mb-4">
              <span className="text-sm font-medium text-primary">
                Our Programs
              </span>
            </div>
            <h1 className="text-3xl md:text-4xl font-bold mb-4">
              Building a Better World, One SDG at a Time
            </h1>
            <p className="text-base md:text-lg text-gray-600 max-w-3xl mx-auto">
              Through our targeted programs, we address critical global
              challenges while empowering women and youth. Each initiative
              aligns with specific Sustainable Development Goals.
            </p>
          </div>

          {/* SDG Selector */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 max-w-5xl mx-auto">
            {SDG_DATA.map((sdg) => (
              <button
                key={sdg.id}
                onClick={() => setActiveSDG(sdg.id)}
                className={`p-4 rounded-xl transition-all duration-300 ${
                  activeSDG === sdg.id
                    ? "bg-white shadow-md border border-primary/20"
                    : "bg-gray-50 hover:bg-white border border-transparent hover:border-gray-200"
                }`}
              >
                <div className="flex items-center gap-3">
                  <div
                    className="p-2 rounded-lg"
                    style={{ backgroundColor: `${sdg.color}20` }}
                  >
                    <Image
                      src={sdg.icon}
                      alt={sdg.title}
                      width={24}
                      height={24}
                      className="w-6 h-6"
                    />
                  </div>
                  <div className="text-left">
                    <p className="text-xs font-medium text-gray-500">
                      SDG {sdg.id}
                    </p>
                    <h3
                      className={`text-sm font-semibold ${
                        activeSDG === sdg.id ? "text-gray-900" : "text-gray-700"
                      }`}
                    >
                      {sdg.title}
                    </h3>
                  </div>
                </div>
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* SDG Content Section */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* SDG Header */}
        <div className="flex items-center gap-4 mb-6 p-6 rounded-xl bg-white shadow-sm border border-gray-100">
          <div
            className="p-4 rounded-xl"
            style={{ backgroundColor: currentSDG.color + "20" }}
          >
            <Image
              src={currentSDG.icon}
              alt={currentSDG.title}
              width={40}
              height={40}
            />
          </div>
          <div>
            <p className="text-sm font-medium text-gray-500 mb-1">
              SDG {currentSDG.id}
            </p>
            <h2 className="text-2xl font-bold">{currentSDG.title}</h2>
          </div>
        </div>

        {/* Impact and Description */}
        <div className="grid md:grid-cols-5 gap-6 mb-10">
          <div className="md:col-span-3">
            <div className="p-6 rounded-xl bg-white shadow-sm border border-gray-100 h-full">
              <h3 className="text-lg font-semibold mb-3">
                About This Initiative
              </h3>
              <p className="text-gray-600">{currentSDG.description}</p>
            </div>
          </div>
          <div className="md:col-span-2">
            <div
              className={`${currentSDG.bgColor} p-6 rounded-xl shadow-sm border ${currentSDG.borderColor} h-full`}
            >
              <h3 className="text-lg font-semibold mb-3">Key Impact</h3>
              <div className="flex items-start gap-3">
                <div
                  className={`mt-1 flex w-10 h-10 flex-none items-center justify-center rounded-lg ${currentSDG.textColor} font-bold`}
                  style={{ backgroundColor: currentSDG.color + "20" }}
                >
                  <span>{currentSDG.id}</span>
                </div>
                <p className="text-gray-700">{currentSDG.impact}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Programs List */}
        <div className="p-6 rounded-xl bg-white shadow-sm border border-gray-100 mb-12">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
            <h3 className="text-xl font-bold">
              Our {currentSDG.title} Programs
            </h3>
            <Link
              href={`/sdg/${currentSDG.id}`}
              className={`text-sm font-medium flex items-center gap-1 ${currentSDG.textColor}`}
            >
              View all programs
              <ChevronRight className="w-4 h-4" />
            </Link>
          </div>

          <div className="grid md:grid-cols-2 gap-4">
            {currentSDG.programs.map((program) => (
              <ProgramCard
                key={program.title}
                program={program}
                color={currentSDG.color}
              />
            ))}
          </div>
        </div>

        {/* Program Features Section */}
        <div className="mb-12">
          <div className="flex flex-col md:flex-row items-start md:items-center justify-between gap-6 mb-8">
            <div>
              <div className="inline-flex items-center gap-2 bg-primary/10 py-1 px-3 rounded-full w-fit mb-3">
                <span className="text-sm font-medium text-primary">
                  What Makes Us Different
                </span>
              </div>
              <h2 className="font-bold text-2xl md:text-3xl">
                Program Features
              </h2>
            </div>
            <p className="text-base text-gray-700 max-w-2xl">
              Our programs go beyond traditional education, integrating
              practical skills with personal development and real-world
              experience.
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-6">
            {PROGRAM_FEATURES.map((feature, index) => (
              <FeatureCard key={index} feature={feature} />
            ))}
          </div>
        </div>

        {/* Featured Image */}
        <div className="relative rounded-xl overflow-hidden aspect-video mb-8">
          <Image
            src={currentSDG.image}
            alt={currentSDG.title}
            fill
            className="object-cover"
          />
          <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/30 to-transparent p-8 flex items-end">
            <div className="text-white">
              <h3 className="text-xl font-bold mb-2">
                {currentSDG.title} Initiatives
              </h3>
              <p className="text-gray-200">
                Our {currentSDG.title.toLowerCase()} initiatives create
                meaningful impact through innovative technology-driven
                approaches.
              </p>
            </div>
          </div>
        </div>

        {/* Call to Action */}
        <div className="bg-primary/5 rounded-xl p-8 text-center">
          <h3 className="text-2xl font-bold mb-3">Ready to Make an Impact?</h3>
          <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
            Join our programs and be part of the change. Whether you're a
            student, mentor, or partner, there's a place for you in our
            community.
          </p>
          <div className="flex flex-wrap justify-center gap-4">
            <Link
              href="/apply"
              className="bg-primary text-white px-6 py-2 rounded-lg hover:bg-primary/90 transition-colors"
            >
              Apply Now
            </Link>
            <Link
              href="/partner"
              className="bg-white border border-gray-200 px-6 py-2 rounded-lg hover:bg-gray-50 transition-colors"
            >
              Partner With Us
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
