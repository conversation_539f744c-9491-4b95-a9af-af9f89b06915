"use client";

import { useState } from "react";
import Image from "next/image";
import Link from "next/link";
import { ChevronRight } from "lucide-react";

type Program = {
  title: string;
  description: string;
  link: string;
  highlights: string[];
};

type SDG = {
  id: number;
  title: string;
  description: string;
  impact: string;
  icon: string;
  image: string;
  color: string;
  programs: Program[];
};

const SDG_DATA: SDG[] = [
  {
    id: 4,
    title: "Quality Education",
    description:
      "Ensuring inclusive and equitable quality education and promoting lifelong learning opportunities for all. Our Tech Skills for Financial Freedom program equips Kenyan youth with cutting-edge, monetizable skills to thrive in the digital economy.",
    impact:
      "Over 1,500 girls have gained essential skills through our education initiatives",
    icon: "/vectors/books.svg",
    image: "/images/school-visits.jpg",
    color: "#c5192d",
    programs: [
      {
        title: "Tech Skills for Financial Freedom",
        description:
          "Empowering girls with essential tech and financial literacy skills to create economic opportunities.",
        link: "/programs/tech-financial-freedom",
        highlights: [
          "Digital literacy fundamentals",
          "Basic coding and web development",
          "Financial planning and entrepreneurship",
          "Personal branding and professional skills",
        ],
      },
      {
        title: "Coding & 3D Design Bootcamps",
        description:
          "Intensive technical training in coding and 3D design to unlock creative and professional potential.",
        link: "/programs/coding-bootcamps",
        highlights: [
          "Web development (HTML, CSS, JavaScript)",
          "3D modeling and design",
          "Prototyping and design thinking",
          "Portfolio development",
        ],
      },
      {
        title: "Financial Independence Workshops",
        description:
          "Empowering girls with financial knowledge and skills to build independent futures.",
        link: "/programs/financial-workshops",
        highlights: [
          "Personal financial management",
          "Investment basics",
          "Entrepreneurship fundamentals",
          "Digital economy opportunities",
        ],
      },
    ],
  },
  {
    id: 5,
    title: "Gender Equality",
    description:
      "Achieve gender equality and empower all women and girls through access to technology and digital skills. Our SGBV initiatives work directly with survivors to provide tech training, counseling, and advocacy.",
    impact:
      "Created safe learning environments for 3,000+ girls, with 78% reporting increased confidence in tech spaces",
    icon: "/vectors/rainbow.svg",
    image: "/images/empowering-image.JPG",
    color: "#ff3a21",
    programs: [
      {
        title: "Ending SGBV, Empowering Survivors",
        description:
          "Comprehensive support program for survivors of sexual and gender-based violence.",
        link: "/programs/empowering-survivors",
        highlights: [
          "Counseling and mental health resources",
          "Digital skills training",
          "Economic empowerment programs",
          "Legal and support network connections",
        ],
      },
      {
        title: "Rescue Center Partnerships",
        description:
          "Collaborative program supporting rescue centers with technology and education.",
        link: "/programs/rescue-centers",
        highlights: [
          "Technology training for center staff",
          "Educational programs for survivors",
          "Digital skill-building workshops",
          "Economic independence support",
        ],
      },
      {
        title: "Advocacy in Action",
        description:
          "Active campaigns and initiatives to promote gender equality and women's rights.",
        link: "/programs/advocacy",
        highlights: [
          "Digital rights awareness",
          "Gender equality in tech",
          "Community education programs",
          "Policy advocacy initiatives",
        ],
      },
    ],
  },
  {
    id: 13,
    title: "Climate Action",
    description:
      "Take urgent action to combat climate change and its impacts through education and sustainable practices. We merge tech with eco-conscious practices through innovative green technologies and sustainable manufacturing.",
    impact:
      "Trained 1,200+ girls in sustainable design principles and eco-friendly manufacturing methods",
    icon: "/vectors/cloud-sun.svg",
    image: "/images/image-3.jpg",
    color: "#3f7e44",
    programs: [
      {
        title: "Sustainable Manufacturing, Responsible Innovation",
        description:
          "Educating girls on sustainable manufacturing and innovative green technologies.",
        link: "/programs/sustainable-manufacturing",
        highlights: [
          "Sustainable design principles",
          "Eco-friendly manufacturing techniques",
          "Circular economy concepts",
          "Green technology innovation",
        ],
      },
      {
        title: "Eco-Friendly 3D Printing",
        description:
          "Exploring sustainable 3D printing technologies and practices.",
        link: "/programs/eco-3d-printing",
        highlights: [
          "Sustainable 3D printing materials",
          "Eco-design principles",
          "Waste reduction techniques",
          "Green manufacturing processes",
        ],
      },
      {
        title: "Green Innovation Hubs",
        description:
          "Fostering innovation and creativity in sustainable technology solutions.",
        link: "/programs/green-innovation",
        highlights: [
          "Climate innovation workshops",
          "Sustainable technology design",
          "Environmental problem-solving",
          "Collaborative green tech projects",
        ],
      },
    ],
  },
  {
    id: 3,
    title: "Good Health & Well-Being",
    description:
      "Ensure healthy lives and promote well-being for all at all ages through technology and community support. Our mental health initiatives support holistic growth through resources, handbooks, and peer networks.",
    impact:
      "Distributed 15,000+ copies of the Teen Self-Help Handbook, reaching girls in 18 countries",
    icon: "/vectors/books.svg",
    image: "/images/cake-cutting.jpg",
    color: "#4c9f38",
    programs: [
      {
        title: "Mental Health for Thriving Futures",
        description:
          "Comprehensive mental health support and education for girls.",
        link: "/programs/mental-health",
        highlights: [
          "Mental health awareness",
          "Coping skills workshops",
          "Peer support networks",
          "Digital wellness resources",
        ],
      },
      {
        title: "Teen Self-Help Handbook",
        description:
          "Comprehensive resource for teen mental health and personal development.",
        link: "/programs/self-help-handbook",
        highlights: [
          "Mental health fundamentals",
          "Self-care strategies",
          "Personal development exercises",
          "Resource directory",
        ],
      },
      {
        title: "Peer Support Networks",
        description:
          "Building supportive communities for mental health and personal growth.",
        link: "/programs/peer-support",
        highlights: [
          "Structured support groups",
          "Mentorship programs",
          "Skill-sharing workshops",
          "Community building",
        ],
      },
    ],
  },
];

const CorporateHero = () => {
  const [activeSDG, setActiveSDG] = useState<number>(4);
  const currentSDG =
    SDG_DATA.find((sdg) => sdg.id === activeSDG) || SDG_DATA[0];

  // Add pulse animation for hover effects
  const pulseAnimation = `
    @keyframes pulse {
      0%, 100% {
        opacity: 1;
      }
      50% {
        opacity: 0.5;
      }
    }
  `;

  return (
    <>
      <style jsx global>{`
        ${pulseAnimation}
      `}</style>
      <section className="relative bg-gradient-to-b from-gray-50 to-white">
        <div className="max-w-7xl mx-auto px-6 lg:px-8 py-24">
          <div className="grid lg:grid-cols-2 gap-16 items-center">
            {/* Left Content */}
            <div className="relative z-10 space-y-8">
              <div className="space-y-4">
                <span className="inline-block text-sm font-medium text-primary bg-primary/10 px-4 py-1.5 rounded-full">
                  Sustainable Development Initiatives
                </span>
                <h1 className="text-4xl font-bold tracking-tight text-gray-900 sm:text-5xl lg:text-6xl">
                  Empowering Futures Through
                  <span className="block mt-2 text-primary">Global Impact</span>
                </h1>
                <p className="text-xl text-gray-600 max-w-2xl">
                  Driving measurable progress across UN Sustainable Development
                  Goals through technology education and community empowerment.
                </p>
              </div>

              {/* SDG Selectors */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                {SDG_DATA.map((sdg) => (
                  <button
                    key={sdg.id}
                    onClick={() => setActiveSDG(sdg.id)}
                    className={`p-4 rounded-xl transition-all duration-300 ${
                      activeSDG === sdg.id
                        ? "bg-white shadow-md border border-primary/20"
                        : "bg-gray-50 hover:bg-white border border-transparent hover:border-gray-200"
                    }`}
                  >
                    <div className="flex items-center gap-3">
                      <div
                        className="p-2 rounded-lg"
                        style={{ backgroundColor: `${sdg.color}15` }}
                      >
                        <Image
                          src={sdg.icon}
                          alt={`SDG ${sdg.id} - ${sdg.title} icon`}
                          width={24}
                          height={24}
                          className="w-6 h-6"
                          quality={75}
                          loading="lazy"
                          placeholder="blur"
                          blurDataURL="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjI0IiBoZWlnaHQ9IjI0IiBmaWxsPSIjZjFmNWY5Ii8+Cjwvc3ZnPgo="
                        />
                      </div>
                      <div className="text-left">
                        <p className="text-xs font-medium text-gray-500">
                          SDG {sdg.id}
                        </p>
                        <h3
                          className={`text-sm font-semibold ${
                            activeSDG === sdg.id
                              ? "text-gray-900"
                              : "text-gray-700"
                          }`}
                        >
                          {sdg.title}
                        </h3>
                      </div>
                    </div>
                  </button>
                ))}
              </div>
            </div>

            {/* Right Content */}
            <div className="relative h-[600px] rounded-3xl overflow-hidden shadow-lg border border-gray-200">
              <Image
                src={currentSDG.image}
                alt={currentSDG.title}
                fill
                className="object-cover"
                priority
              />
              <div className="absolute inset-0 bg-gradient-to-t from-gray-900/60 via-gray-900/20 to-transparent">
                <div className="absolute bottom-8 left-8 right-8 text-white">
                  <div className="flex items-center gap-3 mb-4">
                    <div
                      className="w-10 h-10 rounded-lg flex items-center justify-center"
                      style={{ backgroundColor: `${currentSDG.color}30` }}
                    >
                      <span
                        className="text-sm font-bold"
                        style={{ color: currentSDG.color }}
                      >
                        {currentSDG.id}
                      </span>
                    </div>
                    <h2 className="text-2xl font-bold">{currentSDG.title}</h2>
                  </div>
                  <p className="text-lg opacity-90">{currentSDG.description}</p>
                  <div className="mt-6 flex items-center gap-4">
                    <ChevronRight className="w-6 h-6 text-primary" />
                    <span className="font-medium">
                      Explore {currentSDG.title} Programs
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Metrics - Expanded for better spacing */}
          <div className="mt-20 grid grid-cols-2 md:grid-cols-4 gap-8">
            <div className="bg-white p-8 rounded-xl shadow-sm border border-gray-100 text-center">
              <p className="text-4xl font-bold text-gray-900 mb-3">15K+</p>
              <p className="text-sm text-gray-600">
                Educational Resources Distributed
              </p>
            </div>
            <div className="bg-white p-8 rounded-xl shadow-sm border border-gray-100 text-center">
              <p className="text-4xl font-bold text-gray-900 mb-3">78%</p>
              <p className="text-sm text-gray-600">Program Completion Rate</p>
            </div>
            <div className="bg-white p-8 rounded-xl shadow-sm border border-gray-100 text-center">
              <p className="text-4xl font-bold text-gray-900 mb-3">4.9★</p>
              <p className="text-sm text-gray-600">Participant Satisfaction</p>
            </div>
            <div className="bg-white p-8 rounded-xl shadow-sm border border-gray-100 text-center">
              <p className="text-4xl font-bold text-gray-900 mb-3">18</p>
              <p className="text-sm text-gray-600">Countries Reached</p>
            </div>
          </div>
        </div>
      </section>
    </>
  );
};

export default function ProgramsPage() {
  const [activeSDG, setActiveSDG] = useState<number>(4);

  return (
    <div className="bg-white lg:mx-16">
      <CorporateHero />
    </div>
  );
}
