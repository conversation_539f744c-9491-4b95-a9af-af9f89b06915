import UniversalCard from "@/components/ui/universal-card";

const resources = [
  {
    id: "1",
    title: "HTML & CSS Fundamentals",
    description:
      "Learn the building blocks of web development with our comprehensive HTML and CSS guide.",
    image: "/images/placeholder-resource.jpg",
    category: "Tutorial",
    tags: ["HTML", "CSS", "Web Development"],
    difficulty: "Beginner",
    duration: "2 hours",
    format: "Interactive",
    link: "/resources/html-css-fundamentals",
    ctaText: "Start Learning",
  },
  {
    id: "2",
    title: "JavaScript Essentials",
    description:
      "Master JavaScript basics including variables, functions, and DOM manipulation.",
    image: "/images/placeholder-resource.jpg",
    category: "Course",
    tags: ["JavaScript", "Programming", "Web Development"],
    difficulty: "Intermediate",
    duration: "4 hours",
    format: "Video",
    link: "/resources/javascript-essentials",
    ctaText: "Watch Course",
  },
  {
    id: "3",
    title: "React for Begin<PERSON>",
    description:
      "Get started with <PERSON>act and build your first component-based application.",
    image: "/images/placeholder-resource.jpg",
    category: "Project",
    tags: ["React", "JavaScript", "Frontend"],
    difficulty: "Intermediate",
    duration: "6 hours",
    format: "Hands-on",
    link: "/resources/react-beginners",
    ctaText: "Build Project",
  },
  {
    id: "4",
    title: "Python Programming Basics",
    description:
      "Introduction to Python programming with practical examples and exercises.",
    image: "/images/placeholder-resource.jpg",
    category: "Tutorial",
    tags: ["Python", "Programming", "Basics"],
    difficulty: "Beginner",
    duration: "3 hours",
    format: "Interactive",
    link: "/resources/python-basics",
    ctaText: "Start Coding",
  },
  {
    id: "5",
    title: "Data Structures & Algorithms",
    description:
      "Master fundamental computer science concepts with visual explanations.",
    image: "/images/placeholder-resource.jpg",
    category: "Course",
    tags: ["Data Structures", "Algorithms", "Computer Science"],
    difficulty: "Advanced",
    duration: "8 hours",
    format: "Theory + Practice",
    link: "/resources/dsa",
    ctaText: "Master Concepts",
  },
  {
    id: "6",
    title: "Git & GitHub Mastery",
    description:
      "Learn version control essentials for collaborative software development.",
    image: "/images/placeholder-resource.jpg",
    category: "Workshop",
    tags: ["Git", "GitHub", "Version Control"],
    difficulty: "Beginner",
    duration: "2 hours",
    format: "Live Demo",
    link: "/resources/git-github",
    ctaText: "Join Workshop",
  },
];

export default function ResourceLibrary() {
  return (
    <section className="py-24 md:py-32 px-5 md:px-10 lg:px-16 bg-white">
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-16">
          <div className="inline-flex items-center gap-2 bg-primary/10 py-2 px-4 rounded-full w-fit mb-6 mx-auto">
            <span className="text-sm font-medium text-primary">
              Learning Resources
            </span>
          </div>
          <h2 className="font-bold text-4xl md:text-5xl mb-6">
            Resource Library
          </h2>
          <p className="text-lg md:text-xl text-gray-700 max-w-4xl mx-auto leading-relaxed">
            Access our comprehensive collection of learning materials,
            tutorials, and guides designed to accelerate your coding journey.
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {resources.map((resource) => (
            <UniversalCard
              key={resource.id}
              type="resource"
              data={{
                id: resource.id,
                title: resource.title,
                description: resource.description,
                link: resource.link,
                image: resource.image,
                category: resource.category,
                tags: resource.tags,
                ctaText: resource.ctaText,
                metadata: {
                  difficulty: resource.difficulty,
                  duration: resource.duration,
                  format: resource.format,
                },
              }}
              layout="grid"
              showImage={false}
              showMeta={true}
              className="h-full hover:shadow-lg transition-all duration-300"
            />
          ))}
        </div>
      </div>
    </section>
  );
}
