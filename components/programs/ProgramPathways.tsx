"use client";

import { <PERSON> } from "lucide-react";
import { ChevronRight } from "lucide-react";
import { Button } from "../ui/button";

export default function ProgramPathways() {
  return (
    <div className="w-full relative py-32 px-5 md:py-40 xl:py-40 text-white overflow-hidden">
      {/* Background image with overlay */}
      <div className="absolute inset-0">
        <div className="absolute inset-0 bg-[url('/images/3d-pin.png')] bg-cover bg-center bg-fixed bg-no-repeat"></div>
        <div className="absolute inset-0 bg-gradient-to-r from-black/60 to-black/40"></div>
        {/* Decorative elements */}
        <div className="absolute bottom-0 left-0 w-full h-16 bg-gradient-to-t from-black/20 to-transparent"></div>
      </div>

      {/* Content positioned above the background */}
      <div className="relative z-10 max-w-7xl mx-auto">
        <div className="flex flex-col md:flex-row gap-16 md:gap-20 lg:gap-24">
          {/* Text content */}
          <div className="flex flex-col gap-8 md:w-1/2">
            <div className="inline-flex items-center gap-2 bg-white/10 backdrop-blur-sm py-2 px-4 rounded-full w-fit">
              <div className="w-2 h-2 bg-primary rounded-full animate-pulse"></div>
              <p className="text-sm font-medium">Program Pathways</p>
            </div>

            <div className="flex flex-col gap-8">
              <h2 className="font-bold text-5xl md:text-6xl leading-tight">
                Your Journey with <span className="text-primary">GirlCode</span>
              </h2>

              <p className="text-lg xl:text-xl text-gray-100 leading-relaxed">
                Every girl{"'"}s path is unique. Our programs are designed with
                flexible entry points to meet you where you are—whether you{"'"}
                re new to technology or ready to advance your skills. From
                beginner workshops to advanced training, we provide continuous
                support throughout your journey.
              </p>

              <div className="flex flex-col sm:flex-row gap-4 mt-4">
                <Button
                  variant="outline"
                  className="border-white text-white hover:bg-white hover:text-black px-8 py-3"
                  onClick={() => (window.location.href = "/programs/pathways")}
                >
                  Explore Pathways
                </Button>

                <Button
                  variant="ghost"
                  className="text-primary hover:bg-white/10 px-8 py-3"
                  onClick={() => (window.location.href = "/programs/pathways")}
                >
                  <span>Success Stories</span>
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>

          {/* Pathway Stages */}
          <div className="md:w-1/2">
            <div className="backdrop-blur-sm bg-black/15 rounded-xl p-10 border border-white/10">
              <div className="grid grid-cols-1 gap-10 md:gap-8">
                {/* Stage 1 */}
                <div className="relative">
                  <div className="absolute -left-6 top-1/2 -translate-y-1/2 w-3 h-20 bg-primary rounded-full"></div>
                  <div className="pl-6 flex items-center gap-8">
                    <div>
                      <div className="flex items-baseline gap-1">
                        <h3 className="font-bold text-4xl md:text-5xl">01</h3>
                      </div>
                      <p className="text-xl text-gray-200">Introduction</p>
                    </div>
                    <div className="hidden md:block h-20 w-px bg-white/20"></div>
                    <p className="hidden md:block text-sm text-gray-300 max-w-[200px] leading-relaxed">
                      Basic digital literacy workshops and introductory coding
                      sessions to build foundational skills
                    </p>
                  </div>
                </div>

                {/* Stage 2 */}
                <div className="relative">
                  <div className="absolute -left-6 top-1/2 -translate-y-1/2 w-3 h-20 bg-green-400 rounded-full"></div>
                  <div className="pl-6 flex items-center gap-8">
                    <div>
                      <div className="flex items-baseline gap-1">
                        <h3 className="font-bold text-4xl md:text-5xl">02</h3>
                      </div>
                      <p className="text-xl text-gray-200">Skill Building</p>
                    </div>
                    <div className="hidden md:block h-20 w-px bg-white/20"></div>
                    <p className="hidden md:block text-sm text-gray-300 max-w-[200px] leading-relaxed">
                      Structured bootcamps, specialized training, and mentorship
                      opportunities for hands-on learning
                    </p>
                  </div>
                </div>

                {/* Stage 3 */}
                <div className="relative">
                  <div className="absolute -left-6 top-1/2 -translate-y-1/2 w-3 h-20 bg-blue-400 rounded-full"></div>
                  <div className="pl-6 flex items-center gap-8">
                    <div>
                      <div className="flex items-baseline gap-1">
                        <h3 className="font-bold text-4xl md:text-5xl">03</h3>
                      </div>
                      <p className="text-xl text-gray-200">
                        Real-World Application
                      </p>
                    </div>
                    <div className="hidden md:block h-20 w-px bg-white/20"></div>
                    <p className="hidden md:block text-sm text-gray-300 max-w-[200px] leading-relaxed">
                      Project-based learning, hackathons, and entrepreneurship
                      support for career development
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
