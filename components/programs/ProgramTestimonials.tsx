"use client";

import UniversalCard from "../ui/universal-card";

export default function ProgramTestimonials() {
  const testimonials = [
    {
      id: "amina-testimonial",
      title: "<PERSON>ina <PERSON>",
      description:
        "Before joining GirlCode's 3D printing program, I had never used a computer for more than basic tasks. Now I'm designing and printing eco-friendly products for my community. The technical skills I've gained have opened doors I never knew existed.",
      image: "/images/profile-4.png",
      author: "<PERSON><PERSON>",
      authorImage: "/images/profile-4.png",
      role: "Climate Action Program Graduate",
    },
    {
      id: "grace-testimonial",
      title: "<PERSON>",
      description:
        "The financial independence workshops changed my entire outlook on my future. I not only learned to code but also how to manage money, build credit, and identify business opportunities. I'm now running a small web design service from my home.",
      image: "/images/profile-2.png",
      author: "<PERSON>",
      authorImage: "/images/profile-2.png",
      role: "Financial Independence Program",
    },
    {
      id: "sarah-testimonial",
      title: "<PERSON>",
      description:
        "As a survivor, I never thought I would find my voice again. The rescue center partnership with GirlCode didn't just teach me technology skills – it gave me a supportive community and the confidence to rebuild my life on my own terms.",
      image: "/images/profile-1.png",
      author: "<PERSON> <PERSON>.",
      authorImage: "/images/profile-1.png",
      role: "Rescue Center Program",
    },
  ];
  return (
    <div className="w-full py-24 md:py-32 px-5 md:px-10 lg:px-16 bg-gray-50">
      <div className="max-w-7xl mx-auto">
        <div className="flex flex-col gap-8 mb-16 text-center">
          <span className="text-primary font-medium text-sm tracking-wider uppercase">
            Voices of Change
          </span>
          <h2 className="font-bold text-4xl md:text-5xl">
            Program Participants Share Their Journey
          </h2>
          <p className="text-lg md:text-xl text-gray-700 max-w-4xl mx-auto leading-relaxed">
            From first-time coders to tech entrepreneurs, hear directly from
            girls whose lives have been transformed through our programs.
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-10">
          {testimonials.map((testimonial) => (
            <UniversalCard
              key={testimonial.id}
              type="testimonial"
              data={testimonial}
              layout="grid"
              showImage={false}
              showMeta={false}
              className="h-full hover:shadow-xl transition-all duration-300"
            />
          ))}
        </div>
      </div>
    </div>
  );
}
