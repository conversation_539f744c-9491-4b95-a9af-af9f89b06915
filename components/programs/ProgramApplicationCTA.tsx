"use client";

import React from "react";
import Image from "next/image";
import { <PERSON> } from "lucide-react";
import { <PERSON><PERSON> } from "../ui/button";

export default function ProgramApplicationCTA() {
  return (
    <div className="w-full py-24 md:py-32 px-5 md:px-10 lg:px-16 bg-gray-50">
      <div className="max-w-7xl mx-auto">
        <div className="relative rounded-2xl overflow-hidden bg-white shadow-lg border border-gray-200">
          {/* Background gradient */}
          <div className="absolute inset-0 bg-gradient-to-r from-primary/5 via-purple-50/50 to-white -z-10"></div>

          {/* Decorative elements */}
          <div className="absolute top-0 right-0 w-64 h-64 bg-primary/5 rounded-full -translate-y-1/2 translate-x-1/2 -z-10"></div>
          <div className="absolute bottom-0 left-0 w-48 h-48 bg-purple-100/20 rounded-full translate-y-1/3 -translate-x-1/4 -z-10"></div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-0">
            {/* Content side */}
            <div className="p-8 md:p-12 lg:p-16 flex flex-col justify-center">
              <div className="inline-flex items-center gap-2 bg-primary/10 py-2 px-4 rounded-full w-fit mb-8">
                <span className="text-sm font-medium text-primary">
                  Applications Open
                </span>
              </div>

              <h2 className="font-bold text-4xl md:text-5xl lg:text-6xl mb-8 leading-tight">
                Ready to Begin Your Journey?
              </h2>

              <p className="text-lg md:text-xl text-gray-700 mb-8 max-w-lg leading-relaxed">
                Our next cohort of programs begins soon. Apply now to secure
                your spot and take the first step toward your tech future.
              </p>

              <div className="flex flex-col sm:flex-row gap-4 mb-6">
                <Button className="bg-primary hover:bg-primary/90 px-8 py-4 text-white font-medium rounded-lg">
                  <Link href={"/apply"} className="flex items-center gap-2">
                    Apply Now
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="16"
                      height="16"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    >
                      <path d="M5 12h14M12 5l7 7-7 7" />
                    </svg>
                  </Link>
                </Button>
                <Button
                  variant={"outline"}
                  className="border-gray-300 hover:border-primary hover:text-primary px-8 py-4 font-medium rounded-lg"
                  onClick={() =>
                    (window.location.href = "/programs/learn-more")
                  }
                >
                  Learn More
                </Button>
              </div>

              <div className="mt-4 border-l-4 border-primary/30 pl-6 py-2 italic text-gray-600 text-base">
                Scholarships and financial assistance available for eligible
                applicants. No prior tech experience required for most programs.
              </div>
            </div>

            {/* Image side */}
            <div className="relative h-64 md:h-80 lg:h-full min-h-[400px]">
              <Image
                src={"/images/certificate-image.jpg"}
                alt="Girls collaborating on a project"
                fill
                className="object-cover"
              />

              {/* Image overlay with info */}
              <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent flex flex-col justify-end p-8">
                <div className="bg-white/90 backdrop-blur-sm rounded-lg p-4 shadow-sm inline-block w-auto self-start">
                  <div className="flex items-center gap-3">
                    <div className="bg-primary/20 p-2 rounded-full">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="16"
                        height="16"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        className="text-primary"
                      >
                        <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
                        <circle cx="9" cy="7" r="4"></circle>
                        <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
                        <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
                      </svg>
                    </div>
                    <div>
                      <p className="text-xs font-semibold">Start Your Future</p>
                      <p className="text-xs text-gray-600">
                        Join thousands of graduates
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Image overlay with timeline */}
              <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-transparent to-transparent flex flex-col justify-end p-6">
                <div className="mb-8">
                  <p className="text-white font-medium mb-4">
                    Program Timeline
                  </p>
                  <div className="flex justify-between">
                    <div className="flex flex-col items-center">
                      <div className="w-2 h-2 bg-white rounded-full mb-2"></div>
                      <div className="bg-black/30 backdrop-blur-sm rounded px-2 py-1">
                        <p className="text-white text-xs font-medium">
                          March 1
                        </p>
                        <p className="text-white text-xs">Applications Open</p>
                      </div>
                    </div>

                    <div className="flex flex-col items-center">
                      <div className="w-2 h-2 bg-white rounded-full mb-2"></div>
                      <div className="bg-black/30 backdrop-blur-sm rounded px-2 py-1">
                        <p className="text-white text-xs font-medium">
                          April 15
                        </p>
                        <p className="text-white text-xs">Selection</p>
                      </div>
                    </div>

                    <div className="flex flex-col items-center">
                      <div className="w-2 h-2 bg-primary rounded-full mb-2"></div>
                      <div className="bg-black/30 backdrop-blur-sm rounded px-2 py-1">
                        <p className="text-white text-xs font-medium">May 1</p>
                        <p className="text-white text-xs">Programs Begin</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
