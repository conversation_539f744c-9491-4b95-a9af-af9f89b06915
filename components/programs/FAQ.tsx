"use client";

import React from "react";
import { <PERSON><PERSON> } from "../ui/button";
import { <PERSON> } from "lucide-react";
import { ChevronRight } from "lucide-react";

export default function FAQ() {
  return (
    <div className="w-full py-24 md:py-32 px-5 md:px-10 lg:px-16 bg-gray-50">
      <div className="max-w-5xl mx-auto">
        <div className="text-center mb-16">
          <div className="inline-flex items-center gap-2 bg-primary/10 py-2 px-4 rounded-full w-fit mx-auto mb-6">
            <span className="text-sm font-medium text-primary">
              Common Questions
            </span>
          </div>
          <h2 className="font-bold text-4xl md:text-5xl mb-6">Program FAQs</h2>
          <p className="text-lg md:text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Find answers to frequently asked questions about our programs,
            application process, and requirements.
          </p>
        </div>

        <div className="space-y-4">
          {/* FAQ Item 1 */}
          <div className="border border-gray-200 rounded-lg overflow-hidden">
            <button className="w-full flex justify-between items-center p-4 text-left font-medium hover:bg-gray-50">
              <span>
                Do I need prior coding experience to join your programs?
              </span>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="20"
                height="20"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <polyline points="6 9 12 15 18 9"></polyline>
              </svg>
            </button>
            <div className="px-4 pb-4">
              <p className="text-gray-600">
                Most of our introductory programs require no prior coding
                experience. We have programs designed specifically for beginners
                that will teach you from the ground up. For advanced programs,
                we may require basic computer literacy or completion of our
                introductory courses.
              </p>
            </div>
          </div>

          {/* FAQ Item 2 */}
          <div className="border border-gray-200 rounded-lg overflow-hidden">
            <button className="w-full flex justify-between items-center p-4 text-left font-medium hover:bg-gray-50">
              <span>
                What is the time commitment required for your programs?
              </span>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="20"
                height="20"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <polyline points="6 9 12 15 18 9"></polyline>
              </svg>
            </button>
            <div className="px-4 pb-4">
              <p className="text-gray-600">
                Our programs vary in length and intensity. Short workshops may
                be just a few hours, while comprehensive bootcamps can run for
                8-12 weeks with 10-15 hours of commitment per week. We offer
                both part-time and full-time options to accommodate different
                schedules.
              </p>
            </div>
          </div>

          {/* FAQ Item 3 */}
          <div className="border border-gray-200 rounded-lg overflow-hidden">
            <button className="w-full flex justify-between items-center p-4 text-left font-medium hover:bg-gray-50">
              <span>Are scholarships or financial assistance available?</span>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="20"
                height="20"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <polyline points="6 9 12 15 18 9"></polyline>
              </svg>
            </button>
            <div className="px-4 pb-4">
              <p className="text-gray-600">
                Yes! We{"'"}re committed to making our programs accessible. We
                offer need-based scholarships, payment plans, and sponsored
                positions through our corporate partnerships. The application
                includes a section where you can indicate financial need.
              </p>
            </div>
          </div>

          {/* FAQ Item 4 */}
          <div className="border border-gray-200 rounded-lg overflow-hidden">
            <button className="w-full flex justify-between items-center p-4 text-left font-medium hover:bg-gray-50">
              <span>What equipment or materials do I need to participate?</span>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="20"
                height="20"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <polyline points="6 9 12 15 18 9"></polyline>
              </svg>
            </button>
            <div className="px-4 pb-4">
              <p className="text-gray-600">
                For most programs, you{"'"}ll need access to a computer with
                internet connection. We provide all necessary software and
                learning materials. For our in-person programs, we have computer
                labs available. We also have a laptop loan program for
                participants who don{"'"}t have access to a computer.
              </p>
            </div>
          </div>
        </div>

        <div className="mt-8 text-center">
          <Button variant="ghost" className="text-primary hover:bg-primary/5">
            <Link href="/programs/faq" className="flex items-center gap-2">
              <span>View All FAQs</span>
              <ChevronRight className="h-4 w-4" />
            </Link>
          </Button>
        </div>
      </div>
    </div>
  );
}
