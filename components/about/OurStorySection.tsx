import Image from "next/image";

export default function OurStorySection() {
  return (
    <section className="py-24 bg-gray-50 relative overflow-hidden">
      {/* ... (keeping the background pattern) */}

      <div className="container mx-auto px-4 md:px-6">
        {/* ... (keeping the section header) */}

        {/* Timeline */}
        <div className="relative">
          {/* Vertical line */}
          <div className="absolute left-1/2 top-0 h-full w-px bg-gradient-to-b from-primary/80 via-primary/50 to-primary/20 transform -translate-x-1/2"></div>

          {/* Founding year dot */}
          <div className="absolute left-1/2 top-0 transform -translate-x-1/2 -translate-y-1/2">
            <div className="flex flex-col items-center">
              <div className="w-10 h-10 rounded-full bg-primary flex items-center justify-center text-white font-bold text-sm shadow-lg">
                2021
              </div>
            </div>
          </div>

          {/* Timeline items */}
          <div className="space-y-16">
            {/* First milestone - The Origin */}
            <div className="flex flex-col md:flex-row items-center">
              <div className="md:w-1/2 md:pr-12 text-right order-2 md:order-1">
                <h3 className="text-xl font-bold text-gray-900 mb-3">
                  The Origin
                </h3>
                <p className="text-gray-600">
                  At 24, while volunteering at a local education centre, Winnie
                  saw younger people struggling to shape their futures. They had
                  ambition, resilience, and hunger for success, yet lacked
                  essential skills. For younger girls, the barriers were even
                  greater. Without access to skills in technology, innovation,
                  or entrepreneurship, they weren't just missing out on
                  jobs—they were being left out of the future entirely. With a
                  background in education and a deep, self-driven love for
                  technology, she couldn't ignore these gaps.
                </p>
              </div>
              {/* ... (keeping the image structure, update src) */}
            </div>

            {/* Second milestone - The Beginning */}
            <div className="flex flex-col md:flex-row items-center">
              <div className="md:w-1/2 mb-8 md:mb-0 order-1 flex justify-end md:justify-center">
                {/* ... (keeping image structure) */}
              </div>
              <div className="md:w-1/2 md:pl-12 text-left order-2">
                <h3 className="text-xl font-bold text-gray-900 mb-3">
                  The Beginning
                </h3>
                <p className="text-gray-600">
                  With nothing but a table, a few computers, and a vision,
                  GirlCode began in an open community space. Our first session
                  had eight children, ages 7 to 14, many of them street kids who
                  had never touched a computer. What started as simple curiosity
                  became a transformation. We didn't just teach them how to use
                  technology—we watched them evolve into creators,
                  problem-solvers, and innovators.
                </p>
              </div>
            </div>

            {/* Third milestone - Evolution */}
            <div className="flex flex-col md:flex-row items-center">
              <div className="md:w-1/2 md:pr-12 text-right order-2 md:order-1">
                <h3 className="text-xl font-bold text-gray-900 mb-3">
                  Growth & Evolution
                </h3>
                <p className="text-gray-600">
                  Starting as a teen mentorship program in 2021, GirlCode has
                  evolved into a comprehensive youth and women empowerment
                  initiative. From that open space, we have grown alongside our
                  beneficiaries, witnessing their journey from digital exclusion
                  to mastering real, tangible skills. This is more than
                  education—it's breaking barriers and proving that the future
                  belongs to those willing to build it.
                </p>
              </div>
              {/* ... (keeping image structure) */}
            </div>

            {/* Present day section */}
            <div className="flex justify-center">
              <div className="w-max p-4 bg-white shadow-lg rounded-lg border border-gray-200">
                <span className="font-semibold text-gray-900">Present Day</span>
              </div>
            </div>
          </div>
        </div>

        {/* Updated testimonial */}
        <div className="mt-24 max-w-3xl mx-auto text-center">
          <blockquote className="relative">
            {/* ... (keeping quote svg) */}
            <p className="text-xl text-gray-700 italic">
              "Because talent exists everywhere—but true change begins when we
              stop waiting for opportunities and start building them."
            </p>
            <footer className="mt-8">
              <div className="flex items-center justify-center">
                <div className="relative w-12 h-12 rounded-full overflow-hidden border-2 border-primary/20">
                  <Image
                    src="/images/gc-21.png"
                    alt="Winnie Gathu"
                    fill
                    className="object-cover"
                  />
                </div>
                <div className="ml-3 text-left">
                  <p className="text-base font-medium text-gray-900">
                    Winnie Gathu
                  </p>
                  <p className="text-sm text-gray-600">
                    Founder & Executive Director
                  </p>
                </div>
              </div>
            </footer>
          </blockquote>
        </div>
      </div>
    </section>
  );
}
