"use client";

import React from "react";
import Image from "next/image";

interface Value {
  color: string;
  iconBg: string;
  textColor: string;
  icon: React.ReactNode;
  title: string;
  description: string;
}

const ValueCard = ({ value }: { value: Value }) => (
  <div
    className={`${value.color} rounded-xl p-6 border border-gray-100 hover:shadow-md transition-shadow group`}
  >
    <div
      className={`${value.iconBg} ${value.textColor} w-12 h-12 rounded-full flex items-center justify-center mb-4 transition-transform group-hover:scale-110`}
    >
      {value.icon}
    </div>
    <h3 className={`text-lg font-bold mb-2 ${value.textColor}`}>
      {value.title}
    </h3>
    <p className="text-sm text-gray-700">{value.description}</p>
  </div>
);

export default function CoreValuesSection() {
  const values: Value[] = [
    {
      color: "bg-blue-50",
      iconBg: "bg-blue-500",
      textColor: "text-blue-500",
      icon: "🎯",
      title: "Excellence",
      description: "Striving for the highest quality in everything we do.",
    },
    {
      color: "bg-pink-50",
      iconBg: "bg-pink-500",
      textColor: "text-pink-500",
      icon: "💪",
      title: "Empowerment",
      description: "Enabling women to reach their full potential in tech.",
    },
    {
      color: "bg-purple-50",
      iconBg: "bg-purple-500",
      textColor: "text-purple-500",
      icon: "🤝",
      title: "Inclusivity",
      description: "Creating a welcoming space for all to learn and grow.",
    },
    {
      color: "bg-green-50",
      iconBg: "bg-green-500",
      textColor: "text-green-500",
      icon: "🌱",
      title: "Innovation",
      description: "Embracing new ideas and creative solutions.",
    },
  ];

  return (
    <section id="values" className="relative py-16 md:py-24 bg-gray-50">
      <div className="container mx-auto px-4 md:px-6">
        <div className="grid lg:grid-cols-[1fr_1.2fr] gap-12 xl:gap-16">
          {/* Image Section */}
          <div className="relative aspect-square md:aspect-[3/4] max-w-xl mx-auto">
            <div className="relative h-full rounded-2xl overflow-hidden shadow-xl">
              <Image
                src="/images/gc-19.jpg"
                alt="GirlCode team working together"
                fill
                className="object-cover"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/30 via-black/5 to-transparent flex flex-col justify-end p-6 md:p-8">
                <h3 className="text-white text-xl font-bold">
                  Our Core Values
                </h3>
                <p className="text-white/80 text-sm mt-2">
                  These principles guide every program we develop, every
                  partnership we form, and every life we touch.
                </p>
              </div>
            </div>

            {/* Decorative Elements */}
            <div className="hidden lg:block absolute -bottom-8 -left-8 w-40 h-40 bg-pink-100/30 rounded-full -z-10" />
            <div className="hidden lg:block absolute -top-4 -right-4 w-24 h-24 bg-blue-100/20 rounded-full -z-10" />
          </div>

          {/* Content Section */}
          <div className="flex flex-col justify-center">
            <header className="mb-8 md:mb-12">
              <div className="h-1 w-12 bg-pink-500 mb-4" />
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                Guided by Purpose
              </h2>
              <p className="text-lg text-gray-600 max-w-prose">
                Our values reflect our commitment to creating meaningful impact
                through technology, education, and advocacy.
              </p>
            </header>

            <div className="grid sm:grid-cols-2 gap-4 md:gap-6">
              {values.map((value, index) => (
                <ValueCard key={index} value={value} />
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
