"use client";

import React from "react";
import Image from "next/image"; 

interface Value {
  title: string;
  description: string;
  icon: string;
}

const ValueCard = ({ value, index }: { value: Value; index: number }) => {
  const backgrounds = [
    "bg-blue-50 border-blue-100",
    "bg-pink-50 border-pink-100",
    "bg-green-50 border-green-100",
    "bg-purple-50 border-purple-100",
    "bg-amber-50 border-amber-100",
    "bg-red-50 border-red-100",
  ];
  const iconBgs = [
    "bg-blue-100 text-blue-600",
    "bg-pink-100 text-pink-600",
    "bg-green-100 text-green-600",
    "bg-purple-100 text-purple-600",
    "bg-amber-100 text-amber-600",
    "bg-red-100 text-red-600",
  ];

  const bg = backgrounds[index % backgrounds.length];
  const iconBg = iconBgs[index % iconBgs.length];

  return (
    <div
      className={`rounded-lg p-6 ${bg} border hover:shadow-md transition-shadow`}
    >
      <div
        className={`w-12 h-12 rounded-full ${iconBg} flex items-center justify-center mb-4`}
      >
        <Image
          src={value.icon}
          alt={value.title}
          width={24}
          height={24}
          onError={(e) => {
            e.currentTarget.src = "/vectors/github.svg";
          }}
        />
      </div>
      <h3 className="text-lg font-bold mb-2">{value.title}</h3>
      <p className="text-gray-700 text-sm">{value.description}</p>
    </div>
  );
};
export default ValueCard;