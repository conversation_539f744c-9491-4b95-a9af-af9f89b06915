"use client";

import HeroSection from "@/components/ui/hero-section";
import { useState } from "react";

export default function AboutHeroSection() {
  const [activeSDG, setActiveSDG] = useState(4);

  // These will be used in the metrics section
  const metrics = [
    { number: "1,500+", label: "Girls Trained" },
    { number: "5", label: "Rescue Centers" },
    { number: "4", label: "SDGs Supported" },
    { number: "15,000+", label: "Handbooks" },
  ];

  return (
    <div className="mt-16 mx-auto">
      {/* Hero Section - Enhanced */}
      <HeroSection
        imageUrl="/vectors/bg.svg"
        height="h-2/3"
        overlay={true}
        overlayOpacity={50}
        className="bg-gray-600"
      >
        {/* Enhanced accents */}
        <div className="absolute -top-24 -right-24 w-64 h-64 rounded-full bg-primary/10 blur-3xl"></div>
        <div className="absolute bottom-0 left-0 w-full h-32 bg-gradient-to-t from-gray-950 to-transparent"></div>

        {/* Content with improved spacing and typography */}
        <div className="relative z-10 mx-auto max-w-5xl px-6 lg:px-8">
          <div className="mx-auto max-w-2xl">
            <div className="mb-2">
              <div className="h-1 w-16 bg-gradient-to-r from-primary to-primary/50 mb-6 rounded-full"></div>
            </div>

            <h1 className="text-4xl font-bold tracking-tight text-white sm:text-5xl md:text-6xl leading-tight">
              Building a Better{" "}
              <span className="text-primary bg-primary/5 px-1 rounded">
                World
              </span>
            </h1>

            <p className="mt-6 text-lg text-gray-300 max-w-xl font-light leading-relaxed bg-black/55 p-3">
              Our programs address critical global challenges while empowering
              women and youth. Each initiative aligns with the Sustainable
              Development Goals to create lasting impact.
            </p>
          </div>

          {/* Key metrics with enhanced styling */}
          <div className="mx-auto mt-16 grid grid-cols-2 gap-x-12 gap-y-10 sm:gap-y-8 lg:grid-cols-4">
            {metrics.map((metric, index) => (
              <div key={index} className="flex flex-col gap-1 group">
                <dd className="text-3xl font-semibold tracking-tight text-white group-hover:text-primary transition-colors">
                  {metric.number.includes("+") ? (
                    <>
                      {metric.number.replace("+", "")}
                      <span className="text-primary">+</span>
                    </>
                  ) : (
                    metric.number
                  )}
                </dd>
                <dt className="text-sm text-gray-400 uppercase tracking-wider font-medium">
                  {metric.label}
                </dt>
              </div>
            ))}
          </div>
        </div>
      </HeroSection>

      {/* Rest of the page would go here */}
    </div>
  );
}
