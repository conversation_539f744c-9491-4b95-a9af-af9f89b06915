"use client";

import Image from "next/image";
import React, { useState } from "react";

// Team member data structure
const teamData = {
  leadership: [
    {
      name: "<PERSON><PERSON>",
      title: "Founder & Executive Director",
      bio: "<PERSON><PERSON> combines expertise in Education, Technology, and Community Development to create opportunities for marginalized communities. With an EdTech background, she designs innovative solutions to expand learning access for disadvantaged girls, driven by her passion for gender equity and equitable resource distribution.",
      image: "/images/gc-21.png",
      linkedin: "#",
      twitter: "#",
    },
    {
      name: "<PERSON>",
      title: "Programs and Tech Lead",
      bio: "<PERSON> oversees the development and implementation of all GirlCode programs. With over 10 years of experience in education and program management, he ensures our initiatives create meaningful and measurable impact.",
      image: "/images/profile-moses.jpg",
      linkedin: "#",
      twitter: "#",
    },
    {
      name: "<PERSON><PERSON><PERSON>",
      title: "Director of Partnerships",
      bio: "<PERSON><PERSON><PERSON> builds and maintains GirlCode's crucial relationships with funders, implementing partners, and government agencies. Her background in business development and non-profit management helps GirlCode expand its reach and impact.",
      image: "/images/profile-wanjiku.jpg",
      linkedin: "#",
      twitter: "#",
    },
  ],
  programLeads: [
    {
      name: "<PERSON>",
      title: "Lead, Tech Skills Program",
      bio: "A certified software developer and passionate educator, <PERSON> develops curriculum and leads our coding and 3D design bootcamps.",
      image: "/images/profile-moses.jpg",
      linkedin: "#",
      twitter: "#",
    },
    {
      name: "Joy Akinyi",
      title: "Lead, Gender Equality Initiatives",
      bio: "With expertise in gender studies and trauma-informed care, Joy coordinates our partnerships with rescue centers and advocacy campaigns.",
      image: "/images/profile-3.png",
      linkedin: "#",
      twitter: "#",
    },
    {
      name: "Sylvester Kabiru",
      title: "Lead, Climate Action Program",
      bio: "Simon brings environmental science knowledge and maker experience to our sustainable manufacturing and green innovation initiatives.",
      image: "/images/profile-sly.jpg",
      linkedin: "#",
      twitter: "#",
    },
    {
      name: "Fatuma Hassan",
      title: "Lead, Mental Health Program",
      bio: "A trained counselor specializing in youth mental health, Fatuma developed our Teen Self-Help Handbook and coordinates our peer support networks.",
      image: "/images/profile-4.png",
      linkedin: "#",
      twitter: "#",
    },
  ],
};

interface TeamDataMember {
  name: string;
  title: string;
  bio: string;
  image: string;
  linkedin?: string;
  twitter?: string;
}

// Enhanced Team Member Card Component
const TeamMemberCard = ({
  member,
  featured = false,
}: {
  member: TeamDataMember;
  featured?: boolean;
}) => {
  const [isHovered, setIsHovered] = useState(false);

  return (
    <div
      className={`group relative overflow-hidden rounded-xl ${
        featured ? "h-full" : ""
      }`}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <div
        className={`relative z-10 h-full flex ${
          featured ? "flex-col" : "md:flex-row"
        } bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden hover:shadow-md transition-all duration-300`}
      >
        {/* Image container */}
        <div
          className={`relative ${
            featured ? "h-72" : "md:w-1/3 h-56 md:h-auto"
          }`}
        >
          <Image
            src={member.image}
            alt={member.name}
            fill
            className="object-cover"
          />
          {/* Gradient overlay */}
          <div
            className={`absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-transparent transition-opacity duration-300 ${
              isHovered ? "opacity-100" : "opacity-0 md:opacity-30"
            }`}
          ></div>

          {/* Social media links */}
          <div className="absolute bottom-4 left-4 flex space-x-2">
            {member.linkedin && (
              <a
                href={member.linkedin}
                className="w-8 h-8 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center hover:bg-primary/80 transition-colors"
                aria-label={`${member.name}'s LinkedIn`}
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="text-white"
                >
                  <path d="M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z"></path>
                  <rect x="2" y="9" width="4" height="12"></rect>
                  <circle cx="4" cy="4" r="2"></circle>
                </svg>
              </a>
            )}
            {member.twitter && (
              <a
                href={member.twitter}
                className="w-8 h-8 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center hover:bg-primary/80 transition-colors"
                aria-label={`${member.name}'s Twitter`}
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="text-white"
                >
                  <path d="M23 3a10.9 10.9 0 0 1-3.14 1.53 4.48 4.48 0 0 0-7.86 3v1A10.66 10.66 0 0 1 3 4s-4 9 5 13a11.64 11.64 0 0 1-7 2c9 5 20 0 20-11.5a4.5 4.5 0 0 0-.08-.83A7.72 7.72 0 0 0 23 3z"></path>
                </svg>
              </a>
            )}
          </div>
        </div>

        {/* Content container */}
        <div
          className={`p-6 flex-1 flex flex-col ${featured ? "" : "md:py-8"}`}
        >
          <div className="mb-3">
            <h3 className="text-xl font-bold text-gray-900 mb-1 group-hover:text-primary transition-colors">
              {member.name}
            </h3>
            <div className="flex items-center">
              <div className="h-px w-6 bg-primary/50 mr-2"></div>
              <p className="text-primary font-medium text-sm">{member.title}</p>
            </div>
          </div>
          <p className="text-gray-600 text-sm flex-grow">{member.bio}</p>
        </div>
      </div>
    </div>
  );
};

export default function MeetOurTeam() {
  return (
    <section className="py-24 bg-gradient-to-b from-white to-gray-50 relative overflow-hidden">
      {/* Decorative elements */}
      <div className="absolute top-0 right-0 w-1/3 h-1/3 bg-primary/5 -z-10 rounded-bl-full opacity-70"></div>
      <div className="absolute bottom-0 left-0 w-1/4 h-1/4 bg-primary/5 -z-10 rounded-tr-full opacity-70"></div>

      <div className="container mx-auto px-4 md:px-6">
        {/* Section header */}
        <div className="text-center mb-16 max-w-3xl mx-auto">
          <div className="inline-flex items-center justify-center mb-4">
            <span className="h-px w-8 bg-primary/30"></span>
            <span className="mx-3 text-sm font-medium uppercase tracking-wider text-primary">
              Our People
            </span>
            <span className="h-px w-8 bg-primary/30"></span>
          </div>
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-5">
            The Team Behind Our Mission
          </h2>
          <p className="text-lg text-gray-700">
            Meet the dedicated professionals working tirelessly to bring
            GirlCode's vision to life and create lasting change in our
            communities.
          </p>
        </div>

        {/* Leadership Team */}
        <div className="mb-20">
          <div className="flex items-center justify-center mb-10">
            <div className="h-px w-16 bg-gray-200"></div>
            <h3 className="text-2xl font-bold mx-4 text-center">Leadership</h3>
            <div className="h-px w-16 bg-gray-200"></div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {teamData.leadership.map((member) => (
              <TeamMemberCard
                key={member.name}
                member={member}
                featured={true}
              />
            ))}
          </div>
        </div>

        {/* Program Leads */}
        <div className="mb-20">
          <div className="flex items-center justify-center mb-10">
            <div className="h-px w-16 bg-gray-200"></div>
            <h3 className="text-2xl font-bold mx-4 text-center">
              Program Leads
            </h3>
            <div className="h-px w-16 bg-gray-200"></div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {teamData.programLeads.map((member, index) => (
              <TeamMemberCard key={member.name} member={member} />
            ))}
          </div>
        </div>

        {/* Board of Advisors */}
        <div className="rounded-2xl bg-white shadow-sm border border-gray-100 p-8 md:p-10 relative overflow-hidden">
          {/* Decorative element */}
          <div className="absolute -top-10 -right-10 w-40 h-40 bg-primary/5 rounded-full"></div>

          <div className="relative z-10">
            <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-6 mb-10">
              <div>
                <h3 className="text-2xl font-bold mb-2 text-gray-900">
                  Board of Advisors
                </h3>
                <p className="text-gray-700 max-w-3xl">
                  Our diverse advisory board includes experts in technology,
                  education, gender equality, mental health, and environmental
                  sustainability, providing strategic guidance to ensure
                  GirlCode maintains the highest standards in all our work.
                </p>
              </div>

              <div className="flex-shrink-0">
                <button className="bg-primary/10 hover:bg-primary/20 text-primary font-medium px-5 py-2 rounded-lg text-sm transition-colors">
                  Advisory Opportunities
                </button>
              </div>
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-3">
              <div className="bg-gray-50 hover:bg-gray-100 rounded-lg py-3 px-4 text-center transition-colors">
                <span className="text-sm font-medium text-gray-900">
                  Technology Leaders
                </span>
              </div>
              <div className="bg-gray-50 hover:bg-gray-100 rounded-lg py-3 px-4 text-center transition-colors">
                <span className="text-sm font-medium text-gray-900">
                  Education Specialists
                </span>
              </div>
              <div className="bg-gray-50 hover:bg-gray-100 rounded-lg py-3 px-4 text-center transition-colors">
                <span className="text-sm font-medium text-gray-900">
                  Gender Equality Advocates
                </span>
              </div>
              <div className="bg-gray-50 hover:bg-gray-100 rounded-lg py-3 px-4 text-center transition-colors">
                <span className="text-sm font-medium text-gray-900">
                  Mental Health Professionals
                </span>
              </div>
              <div className="bg-gray-50 hover:bg-gray-100 rounded-lg py-3 px-4 text-center transition-colors">
                <span className="text-sm font-medium text-gray-900">
                  Environmental Experts
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Join our team */}
        {/* <div className="mt-16 text-center">
          <h3 className="text-2xl font-bold mb-4">
            Interested in joining our team?
          </h3>
          <p className="text-gray-700 mb-6 max-w-2xl mx-auto">
            We're always looking for passionate individuals who share our vision
            and want to make a difference in the lives of women and girls.
          </p>
          <button className="bg-primary text-white px-6 py-3 rounded-lg font-medium hover:bg-primary/90 transition-colors">
            View Open Positions
          </button>
        </div> */}
      </div>
    </section>
  );
}
