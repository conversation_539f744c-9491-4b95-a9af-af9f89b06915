'use client';

import React from "react";
import ValueCard from "@/components/about/ValueCard";





// Value data structure
const valuesData = [
    {
      title: "Empowerment Through Education",
      description:
        "We believe that knowledge is power. By providing quality education in technology and life skills, we enable individuals to take control of their own futures.",
      icon: "/vectors/github.svg",
    },
    {
      title: "Intersectional Approach",
      description:
        "We recognize that the challenges facing women and youth are interconnected. Our programs address multiple dimensions of well-being—from economic opportunity to mental health.",
      icon: "/vectors/github.svg",
    },
    {
      title: "Innovation with Purpose",
      description:
        "We embrace technology not for its own sake, but as a tool for creating meaningful change. We prioritize innovations that address real community needs.",
      icon: "/vectors/github.svg",
    },
    {
      title: "Inclusivity",
      description:
        "We actively work to make technology accessible to those traditionally excluded—particularly girls, women, and marginalized communities.",
      icon: "/vectors/github.svg",
    },
    {
      title: "Sustainability",
      description:
        "We are committed to practices that support both environmental sustainability and the long-term viability of our programs.",
      icon: "/vectors/github.svg",
    },
    {
      title: "Holistic Development",
      description:
        "We recognize that technical skills alone are not enough. Our programs integrate mental health support, financial literacy, and life skills.",
      icon: "/vectors/github.svg",
    },
  ];

  
export default function OurValue(){
    return (
    
      <section className="bg-gray-50 py-20 px-4">
      <div className="container mx-auto">
        <div className="text-center mb-16">
          <h5 className="text-primary font-medium mb-3">What Guides Us</h5>
          <h2 className="text-3xl md:text-4xl font-bold mb-6">Our Values</h2>
          <p className="text-lg text-gray-700 max-w-3xl mx-auto">
            These core principles shape our approach and drive our commitment
            to creating meaningful change.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {valuesData.map((value, index) => (
            <ValueCard key={index} value={value} index={index} />
          ))}
        </div>
      </div>
    </section>

   
    )
}