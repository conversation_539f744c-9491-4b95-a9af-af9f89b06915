"use client";

import React from "react";
import UniversalCard from "../ui/universal-card";

// Impact Stats
const impactStats = [
  {
    id: "girls-trained",
    title: "Girls Trained",
    value: "2,000+",
    description: "In coding, 3D design, and financial literacy across Kenya",
    image: "/images/placeholder.jpg",
    icon: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
        className="w-6 h-6"
      >
        <path d="M22 10v6M2 10l10-5 10 5-10 5z"></path>
        <path d="M6 12v5c3 3 9 3 12 0v-5"></path>
      </svg>
    ),
    bgColor: "bg-blue-50",
    iconBg: "bg-blue-100",
    textColor: "text-blue-600",
  },
  {
    id: "rescue-centers",
    title: "Rescue Centers",
    value: "5",
    description: "Partnered to create sustainable pathways out of violence",
    image: "/images/placeholder.jpg",
    icon: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
        className="w-6 h-6"
      >
        <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
        <polyline points="9 22 9 12 15 12 15 22"></polyline>
      </svg>
    ),
    bgColor: "bg-pink-50",
    iconBg: "bg-pink-100",
    textColor: "text-pink-600",
  },
  {
    id: "innovation-platforms",
    title: "Innovation Platforms",
    value: "3",
    description: "Digital platforms connecting the youth to tech opportunities",
    image: "/images/placeholder.jpg",
    icon: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
        className="w-6 h-6"
      >
        <path d="M5 3a2 2 0 0 0-2 2"></path>
        <path d="M19 3a2 2 0 0 1 2 2"></path>
        <path d="M21 19a2 2 0 0 1-2 2"></path>
        <path d="M5 21a2 2 0 0 1-2-2"></path>
        <path d="M9 3h1"></path>
        <path d="M9 21h1"></path>
        <path d="M14 3h1"></path>
        <path d="M14 21h1"></path>
        <path d="M3 9v1"></path>
        <path d="M21 9v1"></path>
        <path d="M3 14v1"></path>
        <path d="M21 14v1"></path>
        <line x1="7" y1="8" x2="17" y2="8"></line>
        <line x1="7" y1="12" x2="17" y2="12"></line>
        <line x1="7" y1="16" x2="17" y2="16"></line>
      </svg>
    ),
    bgColor: "bg-green-50",
    iconBg: "bg-green-100",
    textColor: "text-green-600",
  },
  {
    id: "teens-reached",
    title: "Teens Reached",
    value: "5,000+",
    description: "Through our mental health resources and support networks",
    image: "/images/placeholder.jpg",
    icon: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
        className="w-6 h-6"
      >
        <path d="M20.42 4.58a5.4 5.4 0 0 0-7.65 0l-.77.78-.77-.78a5.4 5.4 0 0 0-7.65 0C1.46 6.7 1.33 10.28 4 13l8 8 8-8c2.67-2.72 2.54-6.3.42-8.42z"></path>
      </svg>
    ),
    bgColor: "bg-purple-50",
    iconBg: "bg-purple-100",
    textColor: "text-purple-600",
  },
];

export default function ImpactStats() {
  return (
    <section className="py-24 relative overflow-hidden">
      {/* Background with accent */}
      <div className="absolute inset-0 bg-gradient-to-b from-white to-gray-50"></div>

      {/* Decorative elements */}
      <div className="absolute top-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-primary/20 to-transparent"></div>
      <div className="absolute bottom-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-primary/20 to-transparent"></div>

      <div className="container mx-auto px-4 md:px-6 relative">
        {/* Section header */}
        <div className="text-center mb-16 max-w-3xl mx-auto">
          <div className="inline-flex items-center justify-center mb-4">
            <span className="h-px w-10 bg-primary/30"></span>
            <span className="mx-3 text-sm font-medium uppercase tracking-wider text-primary">
              Measurable Change
            </span>
            <span className="h-px w-10 bg-primary/30"></span>
          </div>
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-5">
            Our Impact By The Numbers
          </h2>
          <p className="text-lg text-gray-700">
            Since 2021, GirlCode has been creating transformative change in the
            lives of women and girls across Kenya through our community-focused
            initiatives.
          </p>
        </div>

        {/* Stats with animated counters */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-20">
          {impactStats.map((stat, index) => (
            <UniversalCard
              key={stat.id}
              type="impact"
              data={stat}
              layout="grid"
              showImage={false}
              showMeta={false}
            />
          ))}
        </div>

        {/* Impact quote */}
        <div className="max-w-4xl mx-auto bg-white rounded-2xl shadow-sm border border-gray-100 p-8 md:p-10 relative">
          <div className="absolute -top-5 -left-5">
            <div className="bg-primary/10 p-2 rounded-full">
              <svg
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
                className="text-primary"
              >
                <path
                  d="M10 11H6C3.79086 11 2 9.20914 2 7V6C2 3.79086 3.79086 2 6 2H7C9.20914 2 11 3.79086 11 6V7C11 9.20914 9.20914 11 7 11H6"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                />
                <path
                  d="M22 11H18C15.7909 11 14 9.20914 14 7V6C14 3.79086 15.7909 2 18 2H19C21.2091 2 23 3.79086 23 6V7C23 9.20914 21.2091 11 19 11H18"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                />
                <path
                  d="M2 20V22M22 20V22"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                />
                <path
                  d="M10 22V20C10 18.8954 10.8954 18 12 18V18C13.1046 18 14 18.8954 14 20V22"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                />
              </svg>
            </div>
          </div>

          <div className="grid md:grid-cols-5 gap-8 items-center">
            <div className="md:col-span-3">
              <p className="text-xl text-gray-700 italic leading-relaxed">
                "Numbers tell just part of our story. Behind each statistic are
                real young women discovering their potential, survivors
                rebuilding their lives, and communities being transformed
                through technology and education."
              </p>
              <div className="mt-8 flex items-center">
                <div className="w-12 h-12 rounded-full overflow-hidden mr-4">
                  <img
                    src="/images/profile-9.png"
                    alt="Program Director"
                    className="w-full h-full object-cover"
                  />
                </div>
                <div>
                  <p className="font-medium text-gray-900">Jane Muthoni</p>
                  <p className="text-sm text-gray-600">Program Director</p>
                </div>
              </div>
            </div>

            <div className="md:col-span-2">
              <div className="bg-gray-50 rounded-lg p-6">
                <h4 className="font-semibold text-gray-900 mb-4">
                  SDG Alignment
                </h4>
                <div className="grid grid-cols-2 gap-3">
                  <div className="flex items-center gap-2">
                    <div className="w-8 h-8 rounded bg-blue-600 flex items-center justify-center text-white text-xs font-semibold">
                      4
                    </div>
                    <span className="text-sm">Education</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-8 h-8 rounded bg-red-500 flex items-center justify-center text-white text-xs font-semibold">
                      5
                    </div>
                    <span className="text-sm">Gender Equality</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-8 h-8 rounded bg-green-600 flex items-center justify-center text-white text-xs font-semibold">
                      13
                    </div>
                    <span className="text-sm">Climate Action</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-8 h-8 rounded bg-pink-500 flex items-center justify-center text-white text-xs font-semibold">
                      3
                    </div>
                    <span className="text-sm">Good Health</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
