"use client";

import React from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import Link from "next/link";
import { ChevronRight } from "lucide-react";

export default function JoinUsCTA() {
  return (
    <section className="relative py-20 px-4 overflow-hidden">
      <div className="absolute inset-0 bg-[url('/images/image-3.jpg')] bg-cover bg-center bg-no-repeat"></div>
      <div className="absolute inset-0 bg-gradient-to-r from-black/80 to-black/60"></div>

      <div className="container mx-auto relative z-10">
        <div className="max-w-3xl mx-auto text-center text-white">
          <h2 className="text-3xl md:text-4xl font-bold mb-6">
            Join Our Mission
          </h2>
          <p className="text-xl mb-8">
            Together, we can create a future where technology empowers every
            woman and girl to reach her full potential.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            {/* <Button className="bg-primary  hover:bg-primary/90 text-white px-8 py-3 text-lg">
              <Link href="/get-involved">Get Involved</Link>
            </Button> */}
            <Link href="/get-involved" className="flex items-center gap-1">
              <Button className="bg-primary hover:bg-pink-700 text-white ">
                Get Involved
                <ChevronRight className="h-4 w-4" />
              </Button>
            </Link>
            {/* <Button
              variant="outline"
              className="border-white  text-white hover:bg-white/10 px-8 py-3 text-lg"
            >
              <Link href="/contact">Contact Us</Link>
            </Button> */}
          </div>
        </div>
      </div>
    </section>
  );
}
