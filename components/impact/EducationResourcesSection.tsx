"use client";

import React from "react";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ChevronRight, Download, Book, Users } from "lucide-react";
import UniversalCard from "../ui/universal-card";

export default function EducationResourcesSection() {
  const resources = [
    {
      id: "teacher-materials",
      title: "Teacher Materials",
      description:
        "Curriculum guides, lesson plans, and resources for educators teaching technology.",
      link: "/resources/teacher-materials",
      image: "/images/education-image.png",
      icon: <Book className="w-6 h-6 text-blue-600" />,
      color: "bg-blue-600",
      bgColor: "bg-blue-50",
      iconBgColor: "bg-blue-100",
      textColor: "text-blue-600",
      highlights: ["Lesson plans", "Assessment guides", "Activity templates"],
      ctaText: "Access Resources",
      category: "Most Popular",
    },
    {
      id: "self-guided-curriculum",
      title: "Self-Guided Coding Curriculum",
      description:
        "Free, self-paced learning materials for students to develop coding skills independently.",
      link: "/resources/self-guided-curriculum",
      image: "/images/certificate-image.jpg",
      icon: <Download className="w-6 h-6 text-pink-600" />,
      color: "bg-pink-600",
      bgColor: "bg-pink-50",
      iconBgColor: "bg-pink-100",
      textColor: "text-pink-600",
      highlights: ["Beginner Python", "Web development", "Mobile app basics"],
      ctaText: "Access Resources",
      category: "Free Download",
    },
    {
      id: "parent-guides",
      title: "Parent Support Guides",
      description:
        "Resources for parents to support their children's technology education journey.",
      link: "/resources/parent-guides",
      image: "/images/inspiring-change.jpg",
      icon: <Users className="w-6 h-6 text-green-600" />,
      color: "bg-green-600",
      bgColor: "bg-green-50",
      iconBgColor: "bg-green-100",
      textColor: "text-green-600",
      highlights: ["Tech safety tips", "Learning support", "Project ideas"],
      ctaText: "Access Resources",
      category: "Family-Friendly",
    },
  ];

  return (
    <section className="w-full py-20 px-5 md:px-10 lg:px-16 bg-gray-50">
      <div className="max-w-7xl mx-auto">
        {/* Section Header with Decorative Element */}
        <div className="relative mb-20 text-center">
          <div className="absolute left-1/2 -top-14 -translate-x-1/2 w-28 h-28 rounded-full bg-primary/5 blur-2xl"></div>

          <div className="inline-flex items-center gap-2 bg-primary/10 py-1 px-3 rounded-full w-fit mx-auto mb-4">
            <span className="text-sm font-medium text-primary">
              Educational Resources
            </span>
          </div>

          <h2 className="font-bold text-3xl md:text-4xl mb-6">
            Empower Through Education
          </h2>

          <p className="text-gray-600 max-w-3xl mx-auto text-lg leading-relaxed">
            Access our free resources designed to support educators, students,
            and parents in fostering technology education and digital literacy.
          </p>
        </div>

        {/* Resources Grid with Enhanced Cards */}
        <div className="grid md:grid-cols-3 gap-8">
          {resources.map((resource) => (
            <UniversalCard
              key={resource.id}
              type="resource"
              data={resource}
              layout="grid"
              showImage={false}
              showMeta={true}
            />
          ))}
        </div>

        {/* Additional Resource Link */}
        <div className="mt-16 text-center">
          <Link
            href="/resources"
            className="inline-flex items-center gap-2 text-primary font-medium hover:underline group"
          >
            <span>View All Educational Resources</span>
            <ChevronRight className="h-4 w-4 group-hover:translate-x-1 transition-transform" />
          </Link>
        </div>
      </div>
    </section>
  );
}
