"use client";

import React from "react";
import Image from "next/image";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { ChevronRight } from "lucide-react";

export default function PillarsSection() {
  const pillars = [
    {
      image: "/images/certificate-image.jpg",
      title: "Digital Skills",
      description:
        "Empowering girls with coding, design, and digital literacy skills essential for the 21st century workforce.",
      link: "/programs/digital-skills",
      color: "bg-blue-50",
      borderColor: "border-blue-200",
      iconBgColor: "bg-blue-100",
      textColor: "text-blue-600",
      buttonBg: "bg-blue-600",
      icon: (
        <>
          <rect x="2" y="7" width="20" height="14" rx="2" ry="2"></rect>
          <path d="M16 21V5a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16"></path>
        </>
      ),
      highlights: [
        "Coding & Web Development",
        "Digital Literacy",
        "Design Thinking",
      ],
    },
    {
      image: "/images/inspiring-change.jpg",
      title: "Mentorship",
      description:
        "Connecting young women with industry professionals who provide guidance, support and career advice.",
      link: "/programs/mentorship",
      color: "bg-pink-50",
      borderColor: "border-pink-200",
      iconBgColor: "bg-pink-100",
      textColor: "text-pink-600",
      buttonBg: "bg-pink-600",
      icon: (
        <>
          <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
          <circle cx="9" cy="7" r="4"></circle>
          <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
          <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
        </>
      ),
      highlights: [
        "Professional Guidance",
        "Career Development",
        "Networking Opportunities",
      ],
    },
    {
      image: "/images/empowering-image.JPG",
      title: "Safe Spaces",
      description:
        "Creating inclusive environments where girls can learn, collaborate and grow without bias or limitations.",
      link: "/programs/safe-spaces",
      color: "bg-green-50",
      borderColor: "border-green-200",
      iconBgColor: "bg-green-100",
      textColor: "text-green-600",
      buttonBg: "bg-green-600",
      icon: (
        <>
          <path d="M3 18v-6a9 9 0 0 1 18 0v6"></path>
          <path d="M21 19a2 2 0 0 1-2 2h-1a2 2 0 0 1-2-2v-3a2 2 0 0 1 2-2h3zM3 19a2 2 0 0 0 2 2h1a2 2 0 0 0 2-2v-3a2 2 0 0 0-2-2H3z"></path>
        </>
      ),
      highlights: [
        "Inclusive Environments",
        "Gender-Sensitive Training",
        "Community Support",
      ],
    },
  ];

  return (
    <section className="bg-gray-50 py-24">
      <div className="container mx-auto px-6 lg:px-8 max-w-screen-xl">
        {/* Section Header */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center gap-2 bg-primary/10 py-1 px-3 rounded-full mx-auto mb-4">
            <span className="text-sm font-medium text-primary">
              Three-Pillar Approach
            </span>
          </div>
          <h2 className="font-bold text-3xl md:text-4xl mb-4">
            Our Impact Framework
          </h2>
          <p className="text-gray-600 max-w-2xl mx-auto text-lg">
            We approach gender equality in technology through three
            interconnected pillars that create lasting impact across all our
            programs.
          </p>
        </div>

        {/* Pillars Row */}
        <div className="grid lg:grid-cols-3 gap-10">
          {pillars.map((pillar, index) => (
            <div key={index} className="flex flex-col relative group">
              {/* Card with image at top */}
              <div
                className={`rounded-xl shadow-sm overflow-hidden bg-white transition-all h-full group-hover:shadow-md flex flex-col`}
              >
                {/* Image section */}
                <div className="relative h-56 overflow-hidden">
                  <Image
                    src={pillar.image}
                    alt={`${pillar.title} illustration`}
                    fill
                    className="object-cover transition-transform duration-500 group-hover:scale-105"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/30 to-transparent"></div>

                  {/* Floating icon */}
                  <div className="absolute bottom-0 right-6 transform translate-y-1/2">
                    <div
                      className={`${pillar.iconBgColor} rounded-full w-14 h-14 flex items-center justify-center border-4 border-white shadow-sm`}
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="22"
                        height="22"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        className={pillar.textColor}
                      >
                        {pillar.icon}
                      </svg>
                    </div>
                  </div>

                  {/* Pillar number */}
                  <div className="absolute top-4 left-4">
                    <div className="px-3 py-1 bg-white/80 backdrop-blur-sm rounded text-xs font-medium text-gray-800">
                      Pillar {index + 1}
                    </div>
                  </div>
                </div>

                {/* Content section */}
                <div className="p-6 pt-8 flex-grow flex flex-col">
                  <h3 className={`text-xl font-bold mb-3 ${pillar.textColor}`}>
                    {pillar.title}
                  </h3>
                  <p className="text-gray-600 mb-6">{pillar.description}</p>

                  {/* Highlights */}
                  <div className="mt-auto">
                    <div className="mb-6">
                      <h4 className="text-sm font-semibold text-gray-500 mb-3">
                        Key Highlights
                      </h4>
                      <div className="flex flex-wrap gap-2">
                        {pillar.highlights.map((highlight, idx) => (
                          <span
                            key={idx}
                            className={`text-xs px-3 py-1.5 rounded-full ${pillar.color} ${pillar.textColor} border ${pillar.borderColor}`}
                          >
                            {highlight}
                          </span>
                        ))}
                      </div>
                    </div>

                    {/* Button */}
                    <div className="border-t border-gray-100 pt-4">
                      <Link
                        href={pillar.link}
                        className={`flex items-center gap-2 ${pillar.textColor} font-medium text-sm group`}
                      >
                        <span>Explore {pillar.title} Programs</span>
                        <ChevronRight className="h-4 w-4 transition-transform group-hover:translate-x-1" />
                      </Link>
                    </div>
                  </div>
                </div>
              </div>

              {/* Decorative accent */}
              <div
                className={`absolute -bottom-2 left-6 right-6 h-1.5 ${pillar.buttonBg} rounded-b-full transform origin-left transition-transform scale-x-0 group-hover:scale-x-100`}
              ></div>
            </div>
          ))}
        </div>

        {/* Bottom CTA */}
        <div className="text-center mt-16">
          <Link
            href="/programs"
            className="inline-flex items-center gap-2 bg-primary text-white py-3 px-6 rounded-lg hover:bg-primary/90 transition-colors"
          >
            <span>View All Programs</span>
            <ChevronRight className="h-4 w-4" />
          </Link>
        </div>
      </div>
    </section>
  );
}
