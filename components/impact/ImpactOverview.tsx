"use client";

import React from "react";
import UniversalCard from "@/components/ui/universal-card";
import {
  BookOpen,
  Users,
  Laptop,
  Heart,
  Trophy,
  Globe,
  TrendingUp,
  Target,
} from "lucide-react";

export default function ImpactOverview() {
  const impactPillars = [
    {
      icon: BookOpen,
      title: "Quality Education",
      description:
        "Providing accessible, high-quality technology education that bridges the digital divide and creates pathways to opportunity.",
      stats: "2,500+ students trained",
      color: "bg-blue-100",
    },
    {
      icon: Users,
      title: "Gender Equality",
      description:
        "Breaking down barriers and creating inclusive spaces where women and girls can thrive in technology careers.",
      stats: "89% female participation",
      color: "bg-purple-100",
    },
    {
      icon: Heart,
      title: "Health & Wellbeing",
      description:
        "Supporting mental health and overall wellbeing through mentorship, community support, and safe learning environments.",
      stats: "95% improved confidence",
      color: "bg-pink-100",
    },
    {
      icon: Laptop,
      title: "Innovation Hub",
      description:
        "Fostering creativity and innovation through hands-on projects, hackathons, and real-world problem solving.",
      stats: "150+ projects completed",
      color: "bg-green-100",
    },
  ];

  const keyAchievements = [
    {
      title: "Global Reach",
      value: "25 Counties",
      description: "Expanding our impact across continents",
      icon: Globe,
    },
    {
      title: "Career Advancement",
      value: "78%",
      description: "Graduates in tech careers within 6 months",
      icon: TrendingUp,
    },
    {
      title: "Community Impact",
      value: "50+",
      description: "Local partnerships and collaborations",
      icon: Target,
    },
    {
      title: "Awards & Recognition",
      value: "12",
      description: "International awards and accolades",
      icon: Trophy,
    },
  ];

  return (
    <div className="py-16 md:py-24 bg-gradient-to-b from-gray-50 to-white">
      <div className="max-w-7xl mx-auto px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center gap-2 bg-primary/10 py-2 px-4 rounded-full mb-6">
            <div className="w-2 h-2 bg-primary rounded-full animate-pulse"></div>
            <span className="text-sm font-medium text-primary">
              Our Impact Framework
            </span>
          </div>

          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            Transforming Lives Through{" "}
            <span className="text-primary">Four Key Pillars</span>
          </h2>

          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Our comprehensive approach addresses multiple dimensions of
            empowerment, creating sustainable change that extends beyond
            individual participants to entire communities.
          </p>
        </div>

        {/* Impact Pillars Grid */}
        <div className="grid md:grid-cols-2 gap-8 mb-20">
          {impactPillars.map((pillar, index) => {
            const Icon = pillar.icon;
            return (
              <UniversalCard
                key={index}
                type="impact"
                data={{
                  id: `pillar-${index}`,
                  title: pillar.title,
                  description: pillar.description,
                  image: "/images/placeholder-image.jpg",
                  icon: <Icon className="w-6 h-6" />,
                  value: pillar.stats,
                  color: pillar.color,
                  href: "#",
                }}
                className="h-full hover:shadow-xl transition-all duration-300 border-l-4 border-l-primary/20 hover:border-l-primary"
              />
            );
          })}
        </div>

        {/* Key Achievements Section */}
        <div className="bg-white rounded-2xl shadow-lg border border-gray-200 p-8 md:p-12">
          <div className="text-center mb-12">
            <h3 className="text-2xl md:text-3xl font-bold text-gray-900 mb-4">
              Key Achievements & Milestones
            </h3>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Measurable outcomes that demonstrate the effectiveness of our
              programs and the lasting impact on communities worldwide.
            </p>
          </div>

          <div className="grid grid-cols-2 lg:grid-cols-4 gap-6">
            {keyAchievements.map((achievement, index) => {
              const Icon = achievement.icon;
              return (
                <div key={index} className="text-center group">
                  <div className="inline-flex items-center justify-center w-16 h-16 bg-primary/10 rounded-full mb-4 group-hover:bg-primary/20 transition-colors">
                    <Icon className="w-8 h-8 text-primary" />
                  </div>
                  <div className="text-3xl font-bold text-gray-900 mb-2">
                    {achievement.value}
                  </div>
                  <div className="text-sm font-semibold text-gray-700 mb-1">
                    {achievement.title}
                  </div>
                  <div className="text-xs text-gray-500 leading-relaxed">
                    {achievement.description}
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        {/* Call to Action */}
        <div className="text-center mt-16">
          <div className="bg-gray-50 border border-gray-200 rounded-2xl p-8 md:p-12">
            <h3 className="text-2xl md:text-3xl font-bold mb-4 text-gray-900">
              Be Part of Our Growing Impact
            </h3>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto mb-8">
              Join thousands of change-makers who are creating a more inclusive
              and equitable future in technology. Every contribution makes a
              difference.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="bg-primary text-white hover:bg-primary/90 px-8 py-3 rounded-lg font-semibold transition-colors">
                Support Our Mission
              </button>
              <button className="border-2 border-gray-300 text-gray-700 hover:border-primary hover:text-primary px-8 py-3 rounded-lg font-semibold transition-colors">
                Download Impact Report
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
