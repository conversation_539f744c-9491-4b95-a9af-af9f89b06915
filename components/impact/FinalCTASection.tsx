"use client";

import React from "react";
import Image from "next/image";
import Link from "next/link";
import { Button } from "@/components/ui/button";


export default function FinalCTASection () {
    return (
      <div className="w-full py-20 px-5 md:px-10 lg:px-16 bg-white">
        <div className="max-w-7xl mx-auto">
          <div className="relative rounded-2xl overflow-hidden bg-white shadow-lg">
            {/* Background gradient */}
            <div className="absolute inset-0 bg-gradient-to-r from-primary/10 via-purple-50 to-white -z-10"></div>
  
            {/* Decorative elements */}
            <div className="absolute top-0 right-0 w-64 h-64 bg-primary/5 rounded-full -translate-y-1/2 translate-x-1/2 -z-10"></div>
            <div className="absolute bottom-0 left-0 w-48 h-48 bg-purple-100/20 rounded-full translate-y-1/3 -translate-x-1/4 -z-10"></div>
  
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-0">
              {/* Content side */}
              <div className="p-8 md:p-12 lg:p-16 flex flex-col justify-center">
                <h2 className="font-bold text-3xl md:text-4xl lg:text-5xl mb-6 leading-tight">
                  Join Us in Creating Impact
                </h2>
  
                <p className="text-base md:text-lg text-gray-700 mb-8 max-w-lg">
                  Together, we can close the gender gap in technology and create a
                  more equitable future. Your support makes our work possible and
                  empowers the next generation of women in tech.
                </p>
  
                <div className="flex flex-col sm:flex-row gap-4 mb-6">
                  <Button className="bg-primary hover:bg-primary/90 px-6 py-3 text-white font-medium rounded-lg">
                    <Link href={"/donate"} className="flex items-center gap-2">
                      Donate
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="16"
                        height="16"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      >
                        <path d="M5 12h14M12 5l7 7-7 7" />
                      </svg>
                    </Link>
                  </Button>
                  <Button
                    variant={"outline"}
                    className="border-gray-300 hover:border-primary hover:text-primary px-6 py-3 font-medium rounded-lg"
                  >
                    <Link href={"/get-involved"}>Get Involved</Link>
                  </Button>
                </div>
              </div>
  
              {/* Image side */}
              <div className="relative h-64 md:h-80 lg:h-full min-h-[320px]">
                <Image
                  src={"/images/certificate-image.jpg"}
                  alt="Girls collaborating on a project"
                  fill
                  className="object-cover"
                />
  
                {/* Image overlay with info */}
                <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-transparent to-transparent flex flex-col justify-end p-6">
                  <div className="bg-white/90 backdrop-blur-sm rounded-lg p-3 shadow-sm inline-block w-auto self-start">
                    <div className="flex items-center gap-3">
                      <div className="bg-primary/20 p-2 rounded-full">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="16"
                          height="16"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          className="text-primary"
                        >
                          <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
                          <circle cx="9" cy="7" r="4"></circle>
                          <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
                          <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
                        </svg>
                      </div>
                      <div>
                        <p className="text-xs font-semibold">Make a Difference</p>
                        <p className="text-xs text-gray-600">
                          Support our mission
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }
  