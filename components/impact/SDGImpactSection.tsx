"use client";

import React from "react";
import UniversalCard from "@/components/ui/universal-card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Target,
  Users,
  BookOpen,
  Heart,
  Globe,
  Zap,
  ArrowRight,
} from "lucide-react";
import Link from "next/link";

export default function SDGImpactSection() {
  const sdgGoals = [
    {
      id: "sdg-3",
      title: "Good Health and Well-being",
      description:
        "Promoting mental health awareness and providing support systems for young women in technology.",
      image: "/images/sdg-3.jpg",
      category: "SDG 3",
      icon: <Heart className="w-6 h-6" />,
      value: "2,500+ lives impacted",
      color: "bg-green-100",
      bgColor: "bg-green-50",
      textColor: "text-green-600",
      iconBg: "bg-green-100",
      highlights: [
        "Mental health workshops for 1,200+ participants",
        "Peer support networks in 15 communities",
        "Wellness programs integrated into all training",
      ],
      href: "/programs/sdg-3",
    },
    {
      id: "sdg-4",
      title: "Quality Education",
      description:
        "Providing accessible, high-quality technology education that bridges the digital divide.",
      image: "/images/sdg-4.jpg",
      category: "SDG 4",
      icon: <BookOpen className="w-6 h-6" />,
      value: "5,000+ educated",
      color: "bg-blue-100",
      bgColor: "bg-blue-50",
      textColor: "text-blue-600",
      iconBg: "bg-blue-100",
      highlights: [
        "Free coding bootcamps in 25 Counties",
        "Digital literacy programs for all ages",
        "Scholarship programs for underserved communities",
      ],
      href: "/programs/sdg-4",
    },
    {
      id: "sdg-5",
      title: "Gender Equality",
      description:
        "Breaking barriers and creating inclusive spaces where women can thrive in technology careers.",
      image: "/images/sdg-5.jpg",
      category: "SDG 5",
      icon: <Users className="w-6 h-6" />,
      value: "89% female participation",
      color: "bg-purple-100",
      bgColor: "bg-purple-50",
      textColor: "text-purple-600",
      iconBg: "bg-purple-100",
      highlights: [
        "Women-only tech spaces in 20 cities",
        "Female mentorship programs",
        "Leadership development for women",
      ],
      href: "/programs/sdg-5",
    },
    {
      id: "sdg-13",
      title: "Climate Action",
      description:
        "Using technology to address climate challenges and build sustainable communities.",
      image: "/images/sdg-13.jpg",
      category: "SDG 13",
      icon: <Globe className="w-6 h-6" />,
      value: "150+ climate solutions",
      color: "bg-emerald-100",
      bgColor: "bg-emerald-50",
      textColor: "text-emerald-600",
      iconBg: "bg-emerald-100",
      highlights: [
        "Climate hackathons with 800+ participants",
        "Green tech solutions for rural communities",
        "Environmental impact tracking systems",
      ],
      href: "/programs/sdg-13",
    },
  ];

  const globalImpact = [
    {
      icon: Target,
      value: "12",
      label: "SDG Goals",
      description:
        "Contributing to sustainable development across multiple goals",
    },
    {
      icon: Globe,
      value: "25",
      label: "Countries",
      description: "Global reach with local community impact",
    },
    {
      icon: Users,
      value: "5,000+",
      label: "Lives Changed",
      description: "Direct beneficiaries of our SDG-aligned programs",
    },
    {
      icon: Zap,
      value: "78%",
      label: "Success Rate",
      description:
        "Participants achieving program goals and sustainable outcomes",
    },
  ];

  return (
    <div className="py-16 md:py-24 bg-gradient-to-b from-white to-gray-50">
      <div className="max-w-7xl mx-auto px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center gap-2 bg-primary/10 py-2 px-4 rounded-full mb-6">
            <Target className="w-4 h-4 text-primary" />
            <span className="text-sm font-medium text-primary">
              UN Sustainable Development Goals
            </span>
          </div>

          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            Contributing to <span className="text-primary">Global Goals</span>
          </h2>

          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Our programs are strategically aligned with the United Nations
            Sustainable Development Goals, creating measurable impact that
            extends beyond individual participants to entire communities and
            ecosystems.
          </p>
        </div>

        {/* Global Impact Statistics */}
        <div className="bg-white rounded-2xl shadow-lg border border-gray-200 p-8 md:p-12 mb-16">
          <div className="text-center mb-12">
            <h3 className="text-2xl md:text-3xl font-bold text-gray-900 mb-4">
              Our Global SDG Impact
            </h3>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Measurable contributions to the 2030 Agenda for Sustainable
              Development through technology education and community
              empowerment.
            </p>
          </div>

          <div className="grid grid-cols-2 lg:grid-cols-4 gap-6">
            {globalImpact.map((impact, index) => {
              const Icon = impact.icon;
              return (
                <div key={index} className="text-center group">
                  <div className="inline-flex items-center justify-center w-16 h-16 bg-primary/10 rounded-full mb-4 group-hover:bg-primary/20 transition-colors">
                    <Icon className="w-8 h-8 text-primary" />
                  </div>
                  <div className="text-3xl font-bold text-gray-900 mb-2">
                    {impact.value}
                  </div>
                  <div className="text-sm font-semibold text-gray-700 mb-1">
                    {impact.label}
                  </div>
                  <div className="text-xs text-gray-500 leading-relaxed">
                    {impact.description}
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        {/* SDG Goals Grid */}
        <div className="grid md:grid-cols-2 gap-8 mb-16">
          {sdgGoals.map((goal) => (
            <UniversalCard
              key={goal.id}
              type="program"
              data={goal}
              layout="grid"
              className="h-full hover:shadow-xl transition-all duration-300 border-l-4 border-l-primary/20 hover:border-l-primary"
            />
          ))}
        </div>

        {/* Partnership & Recognition */}
        <div className="bg-gradient-to-r from-gray-50 to-gray-100 rounded-2xl p-8 md:p-12 border border-gray-200 mb-16">
          <div className="text-center">
            <h3 className="text-2xl md:text-3xl font-bold mb-4 text-gray-900">
              Recognized by Global Organizations
            </h3>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto mb-8">
              Our SDG-aligned work has been recognized by the United Nations,
              African Union, and other international organizations committed to
              sustainable development.
            </p>

            <div className="grid md:grid-cols-3 gap-6 text-center">
              <div className="bg-white rounded-lg p-6 border border-gray-200 shadow-sm">
                <div className="text-2xl font-bold mb-2 text-gray-900">
                  UN Women
                </div>
                <div className="text-sm text-gray-600">
                  Partnership Agreement
                </div>
              </div>
              <div className="bg-white rounded-lg p-6 border border-gray-200 shadow-sm">
                <div className="text-2xl font-bold mb-2 text-gray-900">
                  African Union
                </div>
                <div className="text-sm text-gray-600">
                  Digital Transformation
                </div>
              </div>
              <div className="bg-white rounded-lg p-6 border border-gray-200 shadow-sm">
                <div className="text-2xl font-bold mb-2 text-gray-900">
                  UNESCO
                </div>
                <div className="text-sm text-gray-600">
                  Education Innovation
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Call to Action */}
        <div className="text-center">
          <div className="bg-gray-50 rounded-2xl p-8 md:p-12">
            <h3 className="text-2xl md:text-3xl font-bold text-gray-900 mb-4">
              Join Our SDG Mission
            </h3>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto mb-8">
              Be part of the global movement for sustainable development. Our
              programs offer multiple pathways to contribute to the 2030 Agenda
              while building your skills and career.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button className="bg-primary hover:bg-primary/90 text-white px-8 py-3">
                <Link href="/programs" className="flex items-center gap-2">
                  Explore SDG Programs
                  <ArrowRight className="w-4 h-4" />
                </Link>
              </Button>
              <Button variant="outline" className="px-8 py-3">
                <Link href="/impact/sdg-report">Download SDG Report</Link>
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
