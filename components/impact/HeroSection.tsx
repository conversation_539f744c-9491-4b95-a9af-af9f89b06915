"use client";

import React from "react";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import HeroSection from "@/components/ui/hero-section";
import { TrendingUp, Users, Globe, Heart } from "lucide-react";

export default function ImpactHeroSection() {
  // Impact metrics data with enhanced styling
  const metrics = [
    {
      number: "5,000+",
      label: "Lives Transformed",
      icon: Users,
      description: "Women and girls empowered through our programs",
    },
    {
      number: "25",
      label: "Countries Reached",
      icon: Globe,
      description: "Global impact across continents",
    },
    {
      number: "89%",
      label: "Success Rate",
      icon: TrendingUp,
      description: "Program completion and career advancement",
    },
    {
      number: "12",
      label: "SDG Goals",
      icon: Heart,
      description: "Contributing to sustainable development",
    },
  ];

  return (
    <div className="mt-16">
      {/* Hero Section */}
      <HeroSection
        imageUrl="/images/ksa-barbah.jpg"
        height="h-2/3"
        overlay={true}
        overlayOpacity={60}
        className="bg-gray-900 relative overflow-hidden"
      >
        {/* Enhanced accents */}
        <div className="absolute top-0 right-0 w-64 h-64 rounded-full bg-primary/10 blur-3xl opacity-50 -translate-y-1/2 translate-x-1/2"></div>
        <div className="absolute bottom-0 left-0 w-full h-32 bg-gradient-to-t from-gray-950/80 to-transparent pointer-events-none"></div>

        {/* Content with improved spacing and typography */}
        <div className="relative z-20 mx-auto max-w-7xl px-6 lg:px-8 h-full flex items-center">
          <div className="grid md:grid-cols-2 gap-12 items-center w-full">
            {/* Left side - Main content */}
            <div className="flex flex-col items-start space-y-6">
              <div className="inline-flex items-center gap-2 bg-primary/20 backdrop-blur-sm py-2 px-4 rounded-full border border-primary/30">
                <TrendingUp className="w-4 h-4 text-primary" />
                <span className="text-sm font-medium text-primary">
                  Measurable Impact
                </span>
              </div>

              <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold tracking-tight text-white leading-tight">
                Creating{" "}
                <span className="text-primary bg-primary/5 px-2 rounded backdrop-blur-sm">
                  Lasting
                </span>{" "}
                Change
              </h1>

              <p className="text-xl text-gray-300 max-w-lg leading-relaxed">
                Through technology education and empowerment programs, we're
                transforming lives and building sustainable futures for women
                and girls worldwide.
              </p>

              <div className="flex flex-col sm:flex-row gap-4">
                <Button className="bg-primary hover:bg-primary/90 text-white px-8 py-3 text-lg">
                  <Link href="/get-involved">Join Our Mission</Link>
                </Button>
                <Button
                  variant="outline"
                  className="border-white text-white hover:bg-white hover:text-gray-900 px-8 py-3 text-lg backdrop-blur-sm"
                >
                  <Link href="#impact-metrics">View Impact Report</Link>
                </Button>
              </div>
            </div>

            {/* Right side - Quick metrics preview */}
            <div className="hidden md:block">
              <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-6">
                <h3 className="text-white text-xl font-semibold mb-6">
                  Our Impact at a Glance
                </h3>
                <div className="grid grid-cols-2 gap-4">
                  {metrics.slice(0, 4).map((metric, index) => {
                    const Icon = metric.icon;
                    return (
                      <div key={index} className="text-center">
                        <div className="bg-primary/20 w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-2">
                          <Icon className="w-6 h-6 text-primary" />
                        </div>
                        <div className="text-2xl font-bold text-white mb-1">
                          {metric.number}
                        </div>
                        <div className="text-sm text-gray-300">
                          {metric.label}
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            </div>
          </div>
        </div>
      </HeroSection>

      {/* Enhanced metrics section below hero */}
      <div className="bg-white py-16" id="impact-metrics">
        <div className="max-w-7xl mx-auto px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Impact Metrics
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Real numbers showing the tangible difference we're making in
              communities around the world.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {metrics.map((metric, index) => {
              const Icon = metric.icon;
              return (
                <div
                  key={index}
                  className="bg-white rounded-2xl p-6 border border-gray-200 hover:shadow-lg transition-all duration-300 text-center group hover:-translate-y-1"
                >
                  <div className="bg-primary/10 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-primary/20 transition-colors">
                    <Icon className="w-8 h-8 text-primary" />
                  </div>
                  <div className="text-3xl font-bold text-gray-900 mb-2">
                    {metric.number}
                  </div>
                  <div className="text-lg font-semibold text-gray-800 mb-2">
                    {metric.label}
                  </div>
                  <div className="text-sm text-gray-600 leading-relaxed">
                    {metric.description}
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>
    </div>
  );
}
