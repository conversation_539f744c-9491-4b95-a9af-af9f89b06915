"use client";

import React from "react";
import UniversalCard from "@/components/ui/universal-card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>R<PERSON>, Star, Users, TrendingUp } from "lucide-react";
import Link from "next/link";

export default function SuccessStoriesSection() {
  const successStories = [
    {
      id: "sarah-johnson",
      title: "From Refugee to Tech Leader",
      description:
        "<PERSON> overcame incredible challenges to become a software developer at a leading tech company, now mentoring other young women in her community.",
      image: "/images/profile-1.png",
      authorImage: "/images/profile-1.png",
      category: "Career Transformation",
      author: "<PERSON>",
      role: "Software Developer, Google",
      date: "2024",
      highlights: [
        "Completed Full-Stack Development Bootcamp",
        "Secured internship at major tech company",
        "Now mentors 15+ young women monthly",
        "Increased family income by 400%",
      ],
      color: "bg-blue-100",
      textColor: "text-blue-700",
      href: "/impact/stories/sarah-johnson",
    },
    {
      id: "maria-santos",
      title: "Building Digital Solutions for Health",
      description:
        "<PERSON> created a mobile health app that now serves over 10,000 users in rural communities, bringing healthcare access to remote areas.",
      image: "/images/profile-2.png",
      authorImage: "/images/profile-2.png",
      category: "Social Innovation",
      author: "Maria Santos",
      role: "Healthcare Tech Entrepreneur",
      date: "2024",
      highlights: [
        "Launched mobile health platform",
        "Serving 10,000+ rural patients",
        "Secured $500K in funding",
        "Featured in WHO innovation report",
      ],
      color: "bg-green-100",
      textColor: "text-green-700",
      href: "/impact/stories/maria-santos",
    },
    {
      id: "aisha-okafor",
      title: "Empowering Communities Through Education",
      description:
        "Aisha founded a coding school for girls in her community, training over 300 young women and creating a pipeline of future tech leaders.",
      image: "/images/profile-3.png",
      authorImage: "/images/profile-3.png",
      category: "Community Leadership",
      author: "Aisha Okafor",
      role: "Founder, CodeGirls Lagos",
      date: "2024",
      highlights: [
        "Founded coding school for girls",
        "Trained 300+ young women",
        "90% job placement rate",
        "Expanded to 5 Nigerian cities",
      ],
      color: "bg-purple-100",
      textColor: "text-purple-700",
      href: "/impact/stories/aisha-okafor",
    },
    {
      id: "fatima-al-zahra",
      title: "Breaking Barriers in Cybersecurity",
      description:
        "Fatima became the first woman in her family to enter cybersecurity, now leads a team protecting critical infrastructure and trains other women in security.",
      image: "/images/profile-4.png",
      authorImage: "/images/profile-4.png",
      category: "Technical Excellence",
      author: "Fatima Al-Zahra",
      role: "Cybersecurity Lead, Emirates Security",
      date: "2024",
      highlights: [
        "First in family to enter tech",
        "Leads cybersecurity team of 12",
        "Trains 50+ women in security",
        "Keynote speaker at 8 conferences",
      ],
      color: "bg-red-100",
      textColor: "text-red-700",
      href: "/impact/stories/fatima-al-zahra",
    },
    {
      id: "priya-sharma",
      title: "AI for Social Good",
      description:
        "Priya developed an AI-powered platform that connects rural farmers with market prices and weather data, impacting over 5,000 farming families.",
      image: "/images/profile-5.png",
      authorImage: "/images/profile-5.png",
      category: "AI Innovation",
      author: "Priya Sharma",
      role: "AI Researcher & Social Entrepreneur",
      date: "2024",
      highlights: [
        "Developed AI platform for farmers",
        "Serving 5,000+ farming families",
        "Reduced crop losses by 40%",
        "Recognized by UN Innovation Lab",
      ],
      color: "bg-orange-100",
      textColor: "text-orange-700",
      href: "/impact/stories/priya-sharma",
    },
    {
      id: "grace-nakimuli",
      title: "Mobile Banking Revolution",
      description:
        "Grace created a mobile banking solution that serves unbanked communities, bringing financial inclusion to over 25,000 people across East Africa.",
      image: "/images/profile-6.png",
      authorImage: "/images/profile-6.png",
      category: "Financial Technology",
      author: "Grace Nakimuli",
      role: "FinTech Founder & CEO",
      date: "2024",
      highlights: [
        "Built mobile banking platform",
        "Serving 25,000+ unbanked people",
        "Processed $2M+ in transactions",
        "Expanded to 6 African countries",
      ],
      color: "bg-teal-100",
      textColor: "text-teal-700",
      href: "/impact/stories/grace-nakimuli",
    },
  ];

  const impactMetrics = [
    {
      icon: Users,
      value: "87%",
      label: "Career Advancement",
      description: "of graduates advance to higher-paying tech roles",
    },
    {
      icon: TrendingUp,
      value: "3.2x",
      label: "Income Increase",
      description: "average salary increase after program completion",
    },
    {
      icon: Star,
      value: "96%",
      label: "Program Satisfaction",
      description: "of participants would recommend our programs",
    },
  ];

  return (
    <div className="py-16 md:py-24 bg-white">
      <div className="max-w-7xl mx-auto px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center gap-2 bg-primary/10 py-2 px-4 rounded-full mb-6">
            <Star className="w-4 h-4 text-primary" />
            <span className="text-sm font-medium text-primary">
              Success Stories
            </span>
          </div>

          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            Real People, Real{" "}
            <span className="text-primary">Transformations</span>
          </h2>

          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Meet the incredible women who have transformed their lives and
            communities through our programs. Their stories inspire and
            demonstrate the lasting impact of technology education.
          </p>
        </div>

        {/* Success Stories Grid */}
        <div className="grid lg:grid-cols-3 gap-8 mb-8">
          {successStories.slice(0, 3).map((story) => (
            <UniversalCard
              key={story.id}
              type="success-story"
              data={story}
              layout="grid"
              className="h-full hover:shadow-xl transition-all duration-300"
            />
          ))}
        </div>

        {/* Additional Stories - More Compact */}
        {successStories.length > 3 && (
          <div className="mb-16">
            <h3 className="text-2xl font-bold text-gray-900 mb-6 text-center">
              More Success Stories
            </h3>
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
              {successStories.slice(3).map((story) => (
                <UniversalCard
                  key={story.id}
                  type="success-story"
                  data={story}
                  layout="compact"
                  className="h-full hover:shadow-lg transition-all duration-300"
                />
              ))}
            </div>
          </div>
        )}

        {/* Impact Metrics */}
        <div className="bg-gray-50 rounded-2xl p-8 md:p-12 mb-12 border border-gray-200">
          <div className="text-center mb-12">
            <h3 className="text-2xl md:text-3xl font-bold text-gray-900 mb-4">
              Measuring Our Impact
            </h3>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              These stories represent more than individual success — they
              demonstrate the ripple effect of empowerment that extends to
              families and communities.
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            {impactMetrics.map((metric, index) => {
              const Icon = metric.icon;
              return (
                <div key={index} className="text-center">
                  <div className="inline-flex items-center justify-center w-16 h-16 bg-primary/10 rounded-full mb-4">
                    <Icon className="w-8 h-8 text-primary" />
                  </div>
                  <div className="text-4xl font-bold text-gray-900 mb-2">
                    {metric.value}
                  </div>
                  <div className="text-lg font-semibold text-gray-700 mb-2">
                    {metric.label}
                  </div>
                  <div className="text-sm text-gray-600 leading-relaxed">
                    {metric.description}
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        {/* Call to Action */}
        <div className="text-center bg-gray-50 border border-gray-200 rounded-2xl p-8 md:p-12">
          <h3 className="text-2xl md:text-3xl font-bold text-gray-900 mb-4">
            Your Story Could Be Next
          </h3>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto mb-8">
            Join thousands of women who have transformed their lives through
            technology. Start your journey today and become part of our global
            community of change-makers.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button className="bg-primary hover:bg-primary/90 text-white px-8 py-3">
              <Link href="/programs" className="flex items-center gap-2">
                Explore Programs
                <ArrowRight className="w-4 h-4" />
              </Link>
            </Button>
            <Button
              variant="outline"
              className="border-gray-300 hover:border-primary hover:text-primary px-8 py-3"
            >
              <Link href="/impact/stories">View All Stories</Link>
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
