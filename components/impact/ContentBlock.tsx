"use client";

import React from "react";
import Image from "next/image";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { ChevronRight } from "lucide-react";

export default function ContentBlock() {
  return (
    <section className="bg-gray-50 py-20">
      <div className="container mx-auto max-w-screen-xl px-6 lg:px-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16">
          {/* Content Column */}
          <div className="flex flex-col justify-center">
            <div className="flex items-center gap-3 mb-6">
              <div className="bg-primary/10 p-3 rounded-full">
                <Image
                  src={"/vectors/github.svg"}
                  alt="Technology icon"
                  height={24}
                  width={24}
                  className="w-6 h-6"
                />
              </div>
              <span className="text-sm font-medium text-primary">
                Our Approach
              </span>
            </div>

            <h2 className="font-semibold text-3xl lg:text-4xl leading-tight mb-8 text-gray-800">
              Empowering Girls Through Technology: A Path to Gender Equality
            </h2>

            <p className="text-gray-600 mb-6 leading-relaxed">
              At GirlCode, we believe that tech education can bridge the gender
              gap in STEM. Through our innovative programs, we{"'"}ve helped
              thousands of girls discover their potential, build confidence, and
              develop skills for the digital economy.
            </p>

            <p className="text-gray-600 mb-8 leading-relaxed">
              Our approach combines hands-on coding experience with mentorship
              and community support. By creating safe, inclusive learning
              environments, we{"'"}re enabling girls to thrive in technology
              fields and shaping a more equitable future.
            </p>

            <h3 className="font-semibold text-xl text-gray-800 mb-4">
              Key Program Features
            </h3>

            <ul className="space-y-3 mb-8">
              {[
                "Hands-On Learning & Project-Based Education",
                "Mentorship & Support from Industry Professionals",
                "Safe Learning Spaces & Inclusive Environment",
                "Forward-Looking Curriculum & Technology Skills",
              ].map((item, index) => (
                <li
                  key={index}
                  className="flex items-start gap-3 text-gray-600"
                >
                  <div className="mt-1 h-4 w-4 rounded-full border border-primary flex items-center justify-center">
                    <div className="h-2 w-2 rounded-full bg-primary"></div>
                  </div>
                  {item}
                </li>
              ))}
            </ul>

            <div className="flex flex-col sm:flex-row gap-6 mt-4">
              <Link
                href="/programs"
                className="inline-flex items-center justify-center px-6 py-3 bg-primary text-white hover:bg-primary/90  transition group"
              >
                <span>Our Programs</span>
                <ChevronRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
              </Link>

              <Link
                href="/about-us"
                className="inline-flex items-center justify-center px-6 py-3 border border-gray-300 text-gray-700 hover:bg-gray-50  transition"
              >
                <span>About Us</span>
              </Link>
            </div>
          </div>

          {/* Image Column */}
          <div className="relative">
            {/* Impact Statement Card */}
            <div className="absolute top-0 right-0 bg-white w-4/5 md:w-3/4 shadow-lg z-10 rounded-lg overflow-hidden">
              <div className="border-l-4 border-primary p-8">
                <span className="text-sm text-gray-500 uppercase tracking-wider font-medium mb-3 block">
                  Our Impact
                </span>
                <h3 className="text-gray-800 font-semibold text-xl md:text-2xl leading-tight">
                  Since 2021, we{"'"}ve helped over 2,000 girls gain technology
                  skills that create pathways to economic independence.
                </h3>
              </div>
            </div>

            {/* Main Image */}
            <div className="mt-20 rounded-lg overflow-hidden shadow-md border border-gray-100">
              <div className="relative h-[400px] lg:h-[480px]">
                <Image
                  src="/images/empowering-image.JPG"
                  alt="Girls learning technology skills"
                  fill
                  className="object-cover"
                />
              </div>
            </div>

            {/* Subtle accent element */}
            <div className="absolute -bottom-4 -left-4 h-24 w-2 bg-primary rounded-full hidden lg:block"></div>

            {/* Background pattern */}
            <div className="absolute -bottom-8 -right-8 w-64 h-64 bg-white rounded-lg -z-10 hidden lg:block"></div>
          </div>
        </div>
      </div>
    </section>
  );
}
