"use client";

import React from "react";
import Link from "next/link";
import Image from "next/image";
import { useTheme } from "next-themes";

const ThemeAwareNavLogo = () => {
  const { theme } = useTheme();

  const logoSrc =
    theme === "dark"
      ? "/logo_assets/logo_white_pink.png"
      : "/logo_assets/logo_black_pink.png";

  return (
    <Link href={"/"}>
      <Image
        src={logoSrc}
        alt="Nav-Logo"
        height={80}
        width={301}
        priority={true}
        className="w-32 md:w-44 xl:w-48 absolute left-1/2 transform -translate-x-1/2 xl:static xl:transform-none -translate-y-1/2"
      />
    </Link>
  );
};

export default ThemeAwareNavLogo;
