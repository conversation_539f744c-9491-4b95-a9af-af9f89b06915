"use client";

import React from "react";
import Link from "next/link";
import Image from "next/image";
import { useTheme } from "next-themes";

const ThemeAwareFooterLogo = () => {
	const { theme } = useTheme();

	const logoSrc =
		theme === "dark"
			? "/logo_assets/logo_footer_white_pink.png"
			: "/logo_assets/logo_footer_black_pink.png";

	return (
		<Link href={"/"}>
			<Image
				src={logoSrc}
				alt="Footer-Logo"
				height={420}
				width={1687}
				priority={true}
				className="w-24"
			/>
		</Link>
	);
};

export default ThemeAwareFooterLogo;
