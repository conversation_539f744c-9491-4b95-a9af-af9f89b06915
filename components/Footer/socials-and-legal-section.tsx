import { socials } from "@/constants";
import Image from "next/image";
import Link from "next/link";
import React from "react";

const SocialAndLegalSection = () => {
	return (
		<div className="flex flex-col gap-6 xl:gap-8">
			{/* Divider */}
			<div className="h-px bg-muted-foreground"></div>
			<div className="flex flex-col pb-4 gap-8 xl:flex-row-reverse xl:pb-0 xl:w-full xl:justify-between">
				<SocialsIconLinks />
				<LegalSection />
			</div>
		</div>
	);
};

const SocialsIconLinks = () => {
	return (
		<div className="flex flex-row gap-3 items-center w-fit">
			{socials.map((item) => {
				return (
					<Link key={item.label} href={item.route} className="dark:invert m-1">
						<Image
							alt="Socials Icon"
							src={item.icon}
							height={24}
							width={24}
							className="w-5"
						/>
					</Link>
				);
			})}
		</div>
	);
};

const LegalSection = () => {
	const currentYear = new Date().getFullYear();

	return (
		<div className="flex flex-col gap-8 text-xs xl:flex-row-reverse xl:items-center">
			<div className="flex flex-col gap-4 xl:flex-row">
				<Link href={"/legal/privacy-policy"} className="underlined-link w-fit">
					Privacy Policy
				</Link>
				<Link
					href={"/legal/terms-of-service"}
					className="underlined-link w-fit"
				>
					Terms of Service
				</Link>
				<Link
					href={"/legal/cookies-settings"}
					className="underlined-link w-fit"
				>
					Cookies Settings
				</Link>
			</div>

			{/* Copyright */}
			<div className="flex items-center">
				© {currentYear} Girl <span>Code</span> Movement. All rights reserved.
			</div>
		</div>
	);
};

export default SocialAndLegalSection;
