"use client";

import Link from "next/link";
import { <PERSON><PERSON> } from "../ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from "../ui/form";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { subscribeToNewsletterFormSchema } from "@/lib/rules";
import { zodResolver } from "@hookform/resolvers/zod";
import { Input } from "../ui/input";
import { toast } from "sonner";

const SubscribeToNewsLetterForm = () => {
  // Define form
  const form = useForm<z.infer<typeof subscribeToNewsletterFormSchema>>({
    resolver: zodResolver(subscribeToNewsletterFormSchema),
    defaultValues: {
      email: "",
    },
  });

  // Define submit handler
  function onSubmit(data: z.infer<typeof subscribeToNewsletterFormSchema>) {
    toast(
      `${data.email} is now successfully subscribed to the GirlCode Movement newsletter. Thank you!`,
      {
        // description: (
        // 	<pre className="mt-2 w-[340px]  bg-slate-950 p-4">
        // 		<code className="text-white">{JSON.stringify(data, null, 2)}</code>
        // 	</pre>
        // )
      }
    );
  }

  return (
    <div className="flex flex-col w-full h-auto gap-4">
      <Form {...form}>
        <form
          onSubmit={form.handleSubmit(onSubmit)}
          className="flex flex-col gap-3 md:flex-row md:gap-4 md:justify-between"
        >
          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormControl>
                  <Input
                    placeholder="Your email here"
                    {...field}
                    className="w-full"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <Button
            type="submit"
            size={"lg"}
            variant={"secondary"}
            className="w-full md:w-1/3"
          >
            Subscribe
          </Button>
        </form>
      </Form>

      <p className="text-xs">
        By subsribing you agree to our{" "}
        <Link href={"/"} className="underlined-link cursor-pointer">
          Privacy Policy
        </Link>{" "}
        and consent to receive updates.
      </p>
    </div>
  );
};

export default SubscribeToNewsLetterForm;
