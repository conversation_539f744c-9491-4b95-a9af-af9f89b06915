import React from "react";
import ThemeAwareFooterLogo from "../Logos/theme-aware-footer-logo";
import {
	FooterColumnLinks,
	footerQuickLinks,
	footerResourcesLinks,
	footerStayConnectedLinks
} from "@/constants";
import Link from "next/link";
import SubscribeToNewsLetterForm from "./subscribe-to-newsletter-form";

const LinksAndFormSection = () => {
	return (
		<div className="flex flex-col gap-12 w-full md:flex-row md:justify-between">
			<Links />
			<SubscribeToNewsLetterSection />
		</div>
	);
};

const Links = () => {
	return (
		<div className="flex flex-col gap-10 h-auto xl:flex-row xl:w-[60%] xl:justify-between">
			<div className="w-fit h-auto">
				<ThemeAwareFooterLogo />
			</div>
			<LinksColumn item={footerQuickLinks} />
			<LinksColumn item={footerResourcesLinks} />
			<LinksColumn item={footerStayConnectedLinks} />
		</div>
	);
};

const LinksColumn = ({ item }: { item: FooterColumnLinks }) => {
	return (
		<div className="w-[166px] h-auto flex flex-col gap-4">
			<div className="h-6 w-auto font-semibold flex items-center text-base">
				{item.heading}
			</div>

			<div className="flex flex-col">
				{item.links.map((link) => {
					return (
						<Link
							key={link.label}
							href={link.route}
							className="h-9 w-fit hover:text-primary/90 flex items-center text-sm"
						>
							{link.label}
						</Link>
					);
				})}
			</div>
		</div>
	);
};

const SubscribeToNewsLetterSection = () => {
	return (
		<div className="flex flex-col gap-4 h-auto max-w-sm">
			{/* Subscribe Heading */}
			<div className="flex flex-col w-full h-auto gap-3">
				<p className="h-6 w-auto font-semibold flex items-center text-base">
					Subscribe
				</p>

				<p className="text-sm">
					Join our newsletter to stay updated on our latest initiatives and
					events.
				</p>
			</div>

			{/* Submit email form */}
			<SubscribeToNewsLetterForm />
		</div>
	);
};

export default LinksAndFormSection;
