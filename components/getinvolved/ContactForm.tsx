"use client";

import React, { useState } from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Mail, MapPin, Phone } from "lucide-react";

export default function ContactForm() {
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    phone: "",
    subject: "",
    message: "",
  });

  const [formStatus, setFormStatus] = useState({
    submitted: false,
    success: false,
    message: "",
  });

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    // Simulate form submission success
    setFormStatus({
      submitted: true,
      success: true,
      message: "Thank you for your message! We'll get back to you soon.",
    });

    // Reset form after submission
    setFormData({
      name: "",
      email: "",
      phone: "",
      subject: "",
      message: "",
    });
  };

  return (
    <div className="w-full py-12 md:py-20 px-5 md:px-10 lg:px-16 bg-gray-50">
      <div className="max-w-6xl mx-auto">
        {/* Section Header */}
        <div className="text-center mb-12">
          <h1 className="text-3xl md:text-4xl font-bold mb-4">Contact Us</h1>
          <p className="text-gray-600 max-w-xl mx-auto">
            Have questions about our programs or want to partner with us? We're
            here to help and would love to hear from you.
          </p>
        </div>

        <div className="grid md:grid-cols-2 gap-8 lg:gap-12">
          {/* Contact Information Side */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
            <div className="bg-primary/5 p-6 border-b border-gray-200">
              <h2 className="font-bold text-2xl mb-2">Get in Touch</h2>
              <p className="text-gray-600">
                We're here to support your journey. Reach out with any questions
                or collaboration ideas.
              </p>
            </div>

            <div className="p-6 space-y-6">
              <div className="flex items-center gap-4 p-4 rounded-lg bg-gray-50 border border-gray-100">
                <Mail className="w-6 h-6 text-primary flex-shrink-0" />
                <div>
                  <p className="text-sm font-medium text-gray-700">Email Us</p>
                  <p className="text-gray-600"><EMAIL></p>
                </div>
              </div>

              <div className="flex items-center gap-4 p-4 rounded-lg bg-gray-50 border border-gray-100">
                <Phone className="w-6 h-6 text-primary flex-shrink-0" />
                <div>
                  <p className="text-sm font-medium text-gray-700">Call Us</p>
                  <p className="text-gray-600">+27 (0) 11 123 4567</p>
                </div>
              </div>

              <div className="flex items-center gap-4 p-4 rounded-lg bg-gray-50 border border-gray-100">
                <MapPin className="w-6 h-6 text-primary flex-shrink-0" />
                <div>
                  <p className="text-sm font-medium text-gray-700">Visit Us</p>
                  <p className="text-gray-600">123 Tech Street, Johannesburg</p>
                </div>
              </div>
            </div>

            <div className="p-6 border-t border-gray-200 bg-gray-50">
              <h3 className="font-medium text-lg mb-3">Office Hours</h3>
              <div className="grid grid-cols-2 gap-2 text-sm text-gray-600">
                <div>Monday - Friday:</div>
                <div>8:00 AM - 5:00 PM</div>
                <div>Saturday:</div>
                <div>9:00 AM - 1:00 PM</div>
                <div>Sunday:</div>
                <div>Closed</div>
              </div>
            </div>
          </div>

          {/* Contact Form Side */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
            <div className="bg-primary/5 p-6 border-b border-gray-200">
              <h2 className="font-bold text-2xl mb-2">Send Us a Message</h2>
              <p className="text-gray-600">
                Fill out the form below and we'll get back to you as soon as
                possible.
              </p>
            </div>

            <div className="p-6">
              {formStatus.submitted && formStatus.success ? (
                <div className="flex flex-col items-center justify-center h-full py-8">
                  <div className="w-16 h-16 rounded-full bg-green-100 flex items-center justify-center mb-4">
                    <svg
                      className="w-8 h-8 text-green-500"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M5 13l4 4L19 7"
                      ></path>
                    </svg>
                  </div>
                  <h3 className="text-xl font-bold mb-2">Thank You!</h3>
                  <p className="text-gray-600 text-center max-w-md mb-4">
                    {formStatus.message}
                  </p>
                  <Button
                    type="button"
                    className="bg-primary hover:bg-primary/90 text-white"
                    onClick={() =>
                      setFormStatus({
                        submitted: false,
                        success: false,
                        message: "",
                      })
                    }
                  >
                    Send Another Message
                  </Button>
                </div>
              ) : (
                <form onSubmit={handleSubmit} className="space-y-6">
                  <div className="grid md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Full Name <span className="text-primary">*</span>
                      </label>
                      <Input
                        type="text"
                        name="name"
                        value={formData.name}
                        onChange={handleChange}
                        required
                        className="w-full border-gray-300 focus:border-primary focus:ring-primary "
                        placeholder="John Doe"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Email Address <span className="text-primary">*</span>
                      </label>
                      <Input
                        type="email"
                        name="email"
                        value={formData.email}
                        onChange={handleChange}
                        required
                        className="w-full border-gray-300 focus:border-primary focus:ring-primary "
                        placeholder="<EMAIL>"
                      />
                    </div>
                  </div>
                  <div className="grid md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Phone Number
                      </label>
                      <Input
                        type="tel"
                        name="phone"
                        value={formData.phone}
                        onChange={handleChange}
                        className="w-full border-gray-300 focus:border-primary focus:ring-primary "
                        placeholder="+27 ************"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Subject <span className="text-primary">*</span>
                      </label>
                      <Input
                        type="text"
                        name="subject"
                        value={formData.subject}
                        onChange={handleChange}
                        required
                        className="w-full border-gray-300 focus:border-primary focus:ring-primary "
                        placeholder="Program Inquiry"
                      />
                    </div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Your Message <span className="text-primary">*</span>
                    </label>
                    <textarea
                      name="message"
                      value={formData.message}
                      onChange={handleChange}
                      required
                      rows={5}
                      className="w-full border border-gray-300  p-2 focus:border-primary focus:ring-primary"
                      placeholder="How can we help you today?"
                    />
                  </div>
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="consent"
                      required
                      className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                    />
                    <label
                      htmlFor="consent"
                      className="ml-2 block text-sm text-gray-700"
                    >
                      I consent to GirlCode storing and processing my personal
                      information per the{" "}
                      <a
                        href="/privacy-policy"
                        className="text-primary hover:underline"
                      >
                        Privacy Policy
                      </a>
                    </label>
                  </div>
                  <Button
                    type="submit"
                    className="w-full bg-primary hover:bg-primary/90 text-white py-3"
                  >
                    Send Message
                  </Button>
                </form>
              )}
            </div>
          </div>
        </div>

        {/* FAQ Section */}
        <div className="mt-16 bg-white rounded-xl shadow-sm border border-gray-200 p-6 md:p-8">
          <h2 className="font-bold text-2xl mb-6 text-center">
            Frequently Asked Questions
          </h2>
          <div className="grid md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <h3 className="font-medium text-lg">How can I join a program?</h3>
              <p className="text-gray-600">
                Check our Programs page for upcoming opportunities and
                application instructions, or contact us directly for
                personalized guidance.
              </p>
            </div>
            <div className="space-y-2">
              <h3 className="font-medium text-lg">
                Do you offer volunteer opportunities?
              </h3>
              <p className="text-gray-600">
                Yes! We welcome volunteers with various skills. Visit our Get
                Involved page to learn about current volunteer needs.
              </p>
            </div>
            <div className="space-y-2">
              <h3 className="font-medium text-lg">
                How can organizations partner with GirlCode?
              </h3>
              <p className="text-gray-600">
                We offer various partnership options from sponsorship to
                collaborative programs. Contact us to discuss potential
                collaboration.
              </p>
            </div>
            <div className="space-y-2">
              <h3 className="font-medium text-lg">
                Where are your programs located?
              </h3>
              <p className="text-gray-600">
                We operate in Johannesburg and surrounding areas, with some
                virtual programs available nationwide.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
