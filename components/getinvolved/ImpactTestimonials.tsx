"use client";

import React from "react";
import UniversalCard from "../ui/universal-card";

export default function ImpactTestimonials() {
  const testimonials = [
    {
      id: "maria-testimonial",
      title: "<PERSON>",
      description:
        "GirlCode didn't just teach me how to code. They gave me the confidence to believe in myself and my potential.",
      image: "/images/inspiring-change.jpg",
      author: "<PERSON>",
      authorImage: "/images/inspiring-change.jpg",
      role: "Software Developer, Program Alumni",
    },
    {
      id: "james-testimonial",
      title: "<PERSON>",
      description:
        "Supporting GirlCode means investing in the future. These young women are solving real-world problems with technology.",
      image: "/images/empowering-image.JPG",
      author: "<PERSON>",
      authorImage: "/images/empowering-image.JPG",
      role: "Corporate Partner",
    },
    {
      id: "sarah-testimonial",
      title: "<PERSON>",
      description:
        "As a volunteer mentor, I've seen incredible transformations. These programs change lives, one skill at a time.",
      image: "/images/certificate-image.jpg",
      author: "<PERSON>",
      authorImage: "/images/certificate-image.jpg",
      role: "Tech Professional & Volunteer",
    },
  ];

  return (
    <div className="w-full py-20 px-5 md:px-10 lg:px-16 bg-white">
      <div className="max-w-7xl mx-auto">
        <div className="flex flex-col gap-6 mb-16 text-center">
          <span className="text-primary font-medium text-sm tracking-wider uppercase mx-auto">
            Real Stories, Real Impact
          </span>
          <h2 className="font-bold text-3xl md:text-4xl">
            Voices of Transformation
          </h2>
          <p className="text-base md:text-lg text-gray-700 max-w-3xl mx-auto">
            Hear from individuals whose lives have been changed through our
            programs and the collective efforts of our supporters.
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {testimonials.map((testimonial) => (
            <UniversalCard
              key={testimonial.id}
              type="testimonial"
              data={testimonial}
              layout="grid"
              showImage={false}
              showMeta={false}
            />
          ))}
        </div>
      </div>
    </div>
  );
}
