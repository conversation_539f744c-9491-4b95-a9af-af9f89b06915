import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, Heart, HandHeart, MessageSquare } from "lucide-react";
import HeroSection from "@/components/ui/hero-section";

export default function GetInvolvedHeroSection() {
  return (
    <div className="mt-16">
      {/* Hero Section */}
      <HeroSection
        imageUrl="/vectors/bg.svg"
        height="h-2/3"
        overlay={true}
        overlayOpacity={60}
        className="bg-gray-900 relative overflow-hidden"
      >
        {/* Enhanced accents */}
        <div className="absolute top-0 right-0 w-64 h-64 rounded-full bg-primary/10 blur-3xl opacity-50 -translate-y-1/2 translate-x-1/2"></div>
        <div className="absolute bottom-0 left-0 w-full h-32 bg-gradient-to-t from-gray-950/80 to-transparent pointer-events-none"></div>

        {/* Content with improved spacing and typography */}
        <div className="relative z-20 mx-auto max-w-7xl px-6 lg:px-8 h-full flex items-center">
          <div className="grid md:grid-cols-2 gap-12 items-center w-full">
            {/* Left side - Main content */}
            <div className="flex flex-col items-start space-y-6">
              <div className="inline-flex items-center gap-2 bg-primary/20 backdrop-blur-sm py-2 px-4 rounded-full border border-primary/30">
                <Heart className="w-4 h-4 text-primary" />
                <span className="text-sm font-medium text-primary">
                  Make a Difference
                </span>
              </div>

              <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold tracking-tight text-white leading-tight">
                Be the{" "}
                <span className="text-primary bg-primary/5 px-2 rounded backdrop-blur-sm">
                  Change
                </span>{" "}
                You Want to See
              </h1>

              <p className="text-xl text-gray-300 max-w-lg leading-relaxed">
                Transform lives, empower communities, and create lasting impact.
                Discover meaningful ways to support our mission of technological
                and social empowerment.
              </p>

              <div className="flex flex-col sm:flex-row gap-4">
                <Button className="bg-primary hover:bg-primary/90 text-white px-8 py-3 text-lg">
                  <Link href="#involvement-options">Ways to Contribute</Link>
                </Button>
                <Button
                  variant="outline"
                  className="border-white text-white hover:bg-white hover:text-gray-900 px-8 py-3 text-lg backdrop-blur-sm"
                >
                  <Link href="#contact-form">Contact Our Team</Link>
                </Button>
              </div>
            </div>

            {/* Right side - Involvement options preview */}
            <div className="hidden md:block">
              <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-6">
                <h3 className="text-white text-xl font-semibold mb-6">
                  How You Can Help
                </h3>

                <div className="space-y-4">
                  {/* Option 1 */}
                  <div className="flex items-center p-3 hover:bg-white/10 rounded-lg transition-colors">
                    <div className="bg-blue-100 p-3 rounded-lg mr-4">
                      <Users className="w-5 h-5 text-blue-600" />
                    </div>
                    <div className="flex-grow">
                      <h4 className="text-white font-medium">Volunteer</h4>
                      <p className="text-sm text-gray-300">
                        Share your skills & expertise
                      </p>
                    </div>
                  </div>

                  {/* Option 2 */}
                  <div className="flex items-center p-3 hover:bg-white/10 rounded-lg transition-colors">
                    <div className="bg-green-100 p-3 rounded-lg mr-4">
                      <HandHeart className="w-5 h-5 text-green-600" />
                    </div>
                    <div className="flex-grow">
                      <h4 className="text-white font-medium">Partner</h4>
                      <p className="text-sm text-gray-300">
                        Collaborate with us
                      </p>
                    </div>
                  </div>

                  {/* Option 3 */}
                  <div className="flex items-center p-3 hover:bg-white/10 rounded-lg transition-colors">
                    <div className="bg-pink-100 p-3 rounded-lg mr-4">
                      <MessageSquare className="w-5 h-5 text-pink-600" />
                    </div>
                    <div className="flex-grow">
                      <h4 className="text-white font-medium">Connect</h4>
                      <p className="text-sm text-gray-300">
                        Join our community
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </HeroSection>
    </div>
  );
}
