"use client";

import { DollarS<PERSON>, <PERSON>, Heart, BookOpen } from "lucide-react";
import { Button } from "@/components/ui/button";
import { ChevronRight } from "lucide-react";
import Link from "next/link";

// Improved component with proper type definitions and button color handling
const InvolvementOption = ({
  title,
  description,
  icon,
  color,
  borderColor,
  buttonColor,
  buttonText,
  link,
}: {
  title: string;
  description: string;
  icon: React.ReactNode;
  color: string;
  borderColor: string;
  buttonColor: string;
  buttonText: string;
  link: string;
}) => (
  <div
    className={`${color} rounded-xl p-8 hover:shadow-md transition-all border ${borderColor} flex flex-col h-full`}
  >
    <div
      className={`rounded-full w-14 h-14 flex items-center justify-center mb-6 bg-opacity-10`}
    >
      {icon}
    </div>

    <h3 className="text-xl font-bold mb-3">{title}</h3>
    <p className="text-gray-700 mb-6 flex-grow">{description}</p>

    <div className="mt-auto">
      <Button className={`${buttonColor} text-white hover:opacity-90`}>
        <Link href={link} className="flex items-center gap-2">
          <span>{buttonText}</span>
          <ChevronRight className="h-4 w-4" />
        </Link>
      </Button>
    </div>
  </div>
);

export default function InvolvementOptions() {
  // Defined option data with proper color mappings
  const optionsData = [
    {
      title: "Donate",
      description:
        "Financial contributions help us develop programs, provide resources, and expand our reach.",
      icon: <DollarSign className="w-8 h-8 text-[#ff3a21]" />,
      color: "bg-red-50",
      borderColor: "border-red-100",
      buttonColor: "bg-[#ff3a21]",
      buttonText: "Support Our Work",
      link: "/donate",
    },
    {
      title: "Volunteer",
      description:
        "Share your skills, mentor young talents, or help with our community initiatives.",
      icon: <Users className="w-8 h-8 text-[#4c9f38]" />,
      color: "bg-green-50",
      borderColor: "border-green-100",
      buttonColor: "bg-[#4c9f38]",
      buttonText: "Join Our Team",
      link: "/volunteer",
    },
    {
      title: "Partner",
      description:
        "Collaborate with us to create sustainable impact through corporate or organizational partnerships.",
      icon: <Heart className="w-8 h-8 text-[#3f7e44]" />,
      color: "bg-emerald-50",
      borderColor: "border-emerald-100",
      buttonColor: "bg-[#3f7e44]",
      buttonText: "Explore Partnerships",
      link: "/partners",
    },
    {
      title: "Learn & Spread",
      description:
        "Educate yourself and others about our mission. Share our story and help raise awareness.",
      icon: <BookOpen className="w-8 h-8 text-[#c5192d]" />,
      color: "bg-blue-50",
      borderColor: "border-blue-100",
      buttonColor: "bg-[#c5192d]",
      buttonText: "Get Informed",
      link: "/resources",
    },
  ];

  return (
    <div
      id="involvement-options"
      className="w-full py-20 px-5 md:px-10 lg:px-16 bg-gray-50"
    >
      <div className="max-w-7xl mx-auto">
        <div className="flex flex-col gap-6 mb-16 text-center">
          <span className="text-primary font-medium text-sm tracking-wider uppercase mx-auto">
            Make a Difference
          </span>
          <h2 className="font-bold text-3xl md:text-4xl">
            Multiple Ways to Support Our Mission
          </h2>
          <p className="text-base md:text-lg text-gray-700 max-w-3xl mx-auto">
            Whether you{"'"}re an individual, a professional, or an
            organization, there are numerous ways to contribute to our goal of
            empowering women and youth through technology.
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
          {optionsData.map((option, index) => (
            <InvolvementOption
              key={index}
              title={option.title}
              description={option.description}
              icon={option.icon}
              color={option.color}
              borderColor={option.borderColor}
              buttonColor={option.buttonColor}
              buttonText={option.buttonText}
              link={option.link}
            />
          ))}
        </div>
      </div>
    </div>
  );
}
