import React from "react";
import HeroS<PERSON>tion from "@/components/ui/hero-section";
import { <PERSON><PERSON> } from "@/components/ui/button";

export default function HeroSectionDemo() {
  return (
    <div className="space-y-8">
      {/* Demo 1: Basic usage with default background */}
      <HeroSection height="h-1/2" imageUrl={null}>
        <h1 className="text-4xl font-bold text-white">
          Welcome to Our Platform
        </h1>
        <p className="text-lg text-gray-200 max-w-2xl">
          This is a demonstration of the HeroSection component with default SVG
          background.
        </p>
        <Button className="bg-primary hover:bg-primary/90 text-white">
          Get Started
        </Button>
      </HeroSection>

      {/* Demo 2: With background image */}
      <HeroSection height="h-2/3" imageUrl="/images/hero-bg.jpg">
        <h1 className="text-5xl font-bold text-white mb-4">
          Transform Your Future
        </h1>
        <p className="text-xl text-gray-200 max-w-3xl mb-6">
          Experience our comprehensive programs designed to empower and educate
          the next generation of leaders.
        </p>
        <div className="flex gap-4">
          <Button className="bg-primary hover:bg-primary/90 text-white">
            Explore Programs
          </Button>
          <Button
            variant="outline"
            className="border-white text-white hover:bg-white/20"
          >
            Learn More
          </Button>
        </div>
      </HeroSection>

      {/* Demo 3: Minimal height */}
      <HeroSection height="h-1/3" imageUrl={null} overlayOpacity={30}>
        <h2 className="text-3xl font-semibold text-white">
          Ready to Get Involved?
        </h2>
        <p className="text-gray-200">Join our community today.</p>
        <Button
          variant="outline"
          className="border-white text-white hover:bg-white/20"
        >
          Contact Us
        </Button>
      </HeroSection>
    </div>
  );
}
