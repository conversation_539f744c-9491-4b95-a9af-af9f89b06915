import React, { forwardRef } from "react";
import { motion } from "motion/react";
import { cn } from "@/lib/utils";

const DropdownMenuContainer = forwardRef<
  HTMLDivElement,
  { children: React.ReactNode; className?: string }
>(({ children, className }, ref) => {
  return (
    <motion.div
      ref={ref}
      initial={{ opacity: 0, scale: 0.75, y: -20 }}
      animate={{ opacity: 1, scale: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className={cn(
        "backdrop-blur-md overflow-hidden border border-border shadow-xl",
        { className }
      )}
    >
      {children}
    </motion.div>
  );
});

DropdownMenuContainer.displayName = "DropdownMenuContainer";

export default DropdownMenuContainer;
