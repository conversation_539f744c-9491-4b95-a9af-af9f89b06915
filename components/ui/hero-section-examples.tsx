// Example usage of the HeroSection component

import React from "react";
import Hero<PERSON><PERSON><PERSON> from "@/components/ui/hero-section";
import { <PERSON><PERSON> } from "@/components/ui/button";
import Link from "next/link";

// Example 1: Hero with background image
export function HeroWithImage() {
  return (
    <HeroSection height="h-screen" imageUrl="/images/hero-background.jpg">
      <h1 className="text-4xl md:text-6xl font-bold text-white mb-4">
        Transform Your Future
      </h1>
      <p className="text-xl text-gray-200 max-w-2xl mb-8">
        Join thousands of women empowering themselves through technology and
        innovation.
      </p>
      <div className="flex flex-col sm:flex-row gap-4">
        <Button className="bg-primary hover:bg-primary/90 text-white px-8 py-3">
          <Link href="/programs">Get Started</Link>
        </Button>
        <Button
          variant="outline"
          className="border-white text-white hover:bg-white/20 px-8 py-3"
        >
          <Link href="/about">Learn More</Link>
        </Button>
      </div>
    </HeroSection>
  );
}

// Example 2: Hero with default SVG background
export function HeroWithDefaultBackground() {
  return (
    <HeroSection height="h-2/3" imageUrl={null}>
      <h1 className="text-3xl md:text-5xl font-bold text-white mb-4">
        Welcome to GirlCode
      </h1>
      <p className="text-lg text-gray-200 max-w-xl mb-6">
        Empowering women and youth through technology education and community
        support.
      </p>
      <Button className="bg-primary hover:bg-primary/90 text-white px-6 py-3">
        Explore Our Programs
      </Button>
    </HeroSection>
  );
}

// Example 3: Shorter hero for inner pages
export function PageHero() {
  return (
    <HeroSection
      height="h-1/2"
      imageUrl="/images/page-bg.jpg"
      overlay={true}
      overlayOpacity={60}
    >
      <div className="inline-flex items-center gap-2 bg-primary/20 py-2 px-4 rounded-full mb-4">
        <span className="text-sm font-medium text-white">Our Programs</span>
      </div>
      <h1 className="text-3xl md:text-4xl font-bold text-white mb-4">
        Building Tomorrow's Leaders
      </h1>
      <p className="text-lg text-gray-200 max-w-lg">
        Discover our comprehensive programs designed to empower the next
        generation.
      </p>
    </HeroSection>
  );
}

// Example 4: Minimal hero with custom styling
export function MinimalHero() {
  return (
    <HeroSection
      height="h-1/3"
      imageUrl={null}
      className="bg-gradient-to-br from-blue-600 to-purple-700"
      overlay={false}
    >
      <h2 className="text-2xl md:text-3xl font-semibold text-white mb-2">
        Ready to Make an Impact?
      </h2>
      <p className="text-gray-200 mb-4">
        Join our community of changemakers today.
      </p>
      <Button
        variant="outline"
        className="border-white text-white hover:bg-white/20"
      >
        Get Involved
      </Button>
    </HeroSection>
  );
}

// Example 5: Complex content layout
export function ComplexHero() {
  return (
    <HeroSection height="h-screen" imageUrl="/images/impact-bg.jpg">
      <div className="text-center max-w-4xl mx-auto">
        <div className="flex justify-center mb-6">
          <div className="bg-white/10 backdrop-blur-sm rounded-full px-4 py-2">
            <span className="text-white text-sm font-medium">
              🚀 New Program Launch
            </span>
          </div>
        </div>

        <h1 className="text-4xl md:text-6xl font-bold text-white mb-6 leading-tight">
          Coding & 3D Design
          <span className="block text-primary">Bootcamp 2025</span>
        </h1>

        <p className="text-xl text-gray-200 mb-8 max-w-2xl mx-auto">
          Intensive 12-week program combining cutting-edge technology skills
          with creative design thinking for the next generation of innovators.
        </p>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8 text-white">
          <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4">
            <div className="text-2xl font-bold">12</div>
            <div className="text-sm opacity-90">Weeks</div>
          </div>
          <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4">
            <div className="text-2xl font-bold">50+</div>
            <div className="text-sm opacity-90">Projects</div>
          </div>
          <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4">
            <div className="text-2xl font-bold">24/7</div>
            <div className="text-sm opacity-90">Support</div>
          </div>
        </div>

        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Button className="bg-primary hover:bg-primary/90 text-white px-8 py-3 text-lg">
            Apply Now - Limited Spots
          </Button>
          <Button
            variant="outline"
            className="border-white text-white hover:bg-white/20 px-8 py-3 text-lg"
          >
            Download Curriculum
          </Button>
        </div>
      </div>
    </HeroSection>
  );
}
