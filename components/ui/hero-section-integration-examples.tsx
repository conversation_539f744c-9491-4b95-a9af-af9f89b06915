// Integration example - Replace an existing hero section with the new component

import HeroSec<PERSON> from "@/components/ui/hero-section";
import { <PERSON><PERSON> } from "@/components/ui/button";
import Link from "next/link";

// Example: Replacing the existing home hero section
export function NewHomeHeroSection() {
  return (
    <HeroSection height="h-screen" imageUrl="/images/gia-1.jpg">
      <div className="h-1 w-12 bg-primary mb-2"></div>
      <h1 className="text-3xl md:text-4xl lg:text-5xl font-semibold tracking-tight text-white">
        Girl<span className="text-primary">Code</span>
      </h1>
      <h2 className="text-2xl md:text-3xl font-light leading-snug text-gray-200">
        Empowering Women & Youth Through Technology
      </h2>
      <p className="text-base md:text-lg text-gray-300 max-w-lg leading-relaxed">
        We equip women and youth with the tools, community, and platforms they
        need to break barriers, achieve financial independence, and drive
        systemic change.
      </p>
      <div className="flex flex-col sm:flex-row gap-4">
        <Button className="bg-gray-800 hover:bg-gray-700 text-white px-6 py-3">
          <Link href="/programs" className="flex items-center">
            <span>Explore Programs</span>
          </Link>
        </Button>
        <Button
          variant="outline"
          className="border-white text-white hover:bg-white/20 px-6 py-3"
        >
          <Link href="/get-involved">Get Involved</Link>
        </Button>
      </div>
    </HeroSection>
  );
}

// Example: Programs page hero
export function ProgramsHeroSection() {
  return (
    <HeroSection height="h-2/3" imageUrl="/images/school-visits.jpg">
      <div className="inline-flex items-center gap-2 bg-primary/10 py-1.5 px-4 rounded-full mb-6">
        <span className="text-sm font-medium text-primary">
          Sustainable Development Initiatives
        </span>
      </div>
      <h1 className="text-4xl font-bold tracking-tight text-white sm:text-5xl lg:text-6xl">
        Empowering Futures Through
        <span className="block mt-2 text-primary">Global Impact</span>
      </h1>
      <p className="text-xl text-gray-300 max-w-2xl">
        Driving measurable progress across UN Sustainable Development Goals
        through technology education and community empowerment.
      </p>
    </HeroSection>
  );
}

// Example: About page hero
export function AboutHeroSection() {
  return (
    <HeroSection height="h-2/3" imageUrl="/vectors/bg.svg">
      <div className="h-1 w-16 bg-gradient-to-r from-primary to-primary/50 mb-6 rounded-full"></div>
      <h1 className="text-4xl font-bold tracking-tight text-white sm:text-5xl md:text-6xl leading-tight">
        Building a Better{" "}
        <span className="text-primary bg-primary/5 px-1 rounded">World</span>
      </h1>
      <p className="text-lg text-gray-300 max-w-xl font-light leading-relaxed bg-black/55 p-3">
        Our programs address critical global challenges while empowering women
        and youth. Each initiative aligns with the Sustainable Development Goals
        to create lasting impact.
      </p>
    </HeroSection>
  );
}
