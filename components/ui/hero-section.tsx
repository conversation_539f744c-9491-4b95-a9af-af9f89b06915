"use client";

import React from "react";
import Image from "next/image";
import { cn } from "@/lib/utils";

// Height mapping for the prop values
const heightMap = {
  "h-1/3": "h-1/3",
  "h-1/2": "h-1/2",
  "h-2/3": "h-2/3",
  "h-screen": "h-screen",
} as const;

// Default SVG graphic to use when no imageUrl is provided
const DefaultSVGBackground = () => (
  <svg
    viewBox="0 0 1200 600"
    className="absolute inset-0 w-full h-full object-cover"
    xmlns="http://www.w3.org/2000/svg"
  >
    <defs>
      <linearGradient id="heroGradient" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" stopColor="#667eea" stopOpacity="0.8" />
        <stop offset="50%" stopColor="#764ba2" stopOpacity="0.6" />
        <stop offset="100%" stopColor="#f093fb" stopOpacity="0.4" />
      </linearGradient>
      <pattern
        id="dots"
        x="0"
        y="0"
        width="40"
        height="40"
        patternUnits="userSpaceOnUse"
      >
        <circle cx="20" cy="20" r="2" fill="white" fillOpacity="0.1" />
      </pattern>
    </defs>

    {/* Background gradient */}
    <rect width="100%" height="100%" fill="url(#heroGradient)" />

    {/* Dot pattern overlay */}
    <rect width="100%" height="100%" fill="url(#dots)" />

    {/* Abstract shapes */}
    <circle cx="200" cy="150" r="80" fill="white" fillOpacity="0.1" />
    <circle cx="1000" cy="450" r="120" fill="white" fillOpacity="0.08" />
    <polygon
      points="800,100 900,180 820,250 720,180"
      fill="white"
      fillOpacity="0.06"
    />
    <rect
      x="100"
      y="400"
      width="150"
      height="150"
      rx="20"
      fill="white"
      fillOpacity="0.05"
      transform="rotate(15 175 475)"
    />
  </svg>
);

export interface HeroSectionProps {
  /**
   * Background image URL. If null, a default SVG graphic will be used.
   */
  imageUrl?: string | null;

  /**
   * Height of the hero section using predefined Tailwind classes
   */
  height: keyof typeof heightMap;

  /**
   * Content to be displayed within the hero section
   */
  children: React.ReactNode;

  /**
   * Additional CSS classes for customization
   */
  className?: string;

  /**
   * Whether to apply a dark overlay over the background
   */
  overlay?: boolean;

  /**
   * Overlay opacity (0-100)
   */
  overlayOpacity?: number;
}

/**
 * A reusable hero section component that accepts background images or uses a default SVG,
 * supports different heights, and provides uniform spacing for children elements.
 */
const HeroSection: React.FC<HeroSectionProps> = ({
  imageUrl,
  height,
  children,
  className,
  overlay = true,
  overlayOpacity = 50,
}) => {
  const heightClass = heightMap[height];

  // Convert children to array to handle spacing uniformly
  const childrenArray = React.Children.toArray(children);

  return (
    <section
      className={cn(
        "relative w-full overflow-hidden flex items-center justify-center",
        heightClass,
        className
      )}
    >
      {/* Background Layer */}
      <div className="absolute inset-0 z-0">
        {!imageUrl ? (
          // No background when null is explicitly provided
          <></>
        ) : imageUrl === "default" ? (
          // Default SVG background when "default" string is provided
          <DefaultSVGBackground />
        ) : imageUrl ? (
          // Image background when URL is provided
          <Image
            src={imageUrl}
            alt="Hero background"
            fill
            className="object-cover object-center"
            priority
            quality={85}
            sizes="100vw"
            placeholder="blur"
            blurDataURL="data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAAIAAoDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAhEAACAQMDBQAAAAAAAAAAAAABAgMABAUGIWGRkrHB0f/EABUBAQEAAAAAAAAAAAAAAAAAAAMF/8QAGhEAAgIDAAAAAAAAAAAAAAAAAAECEgMRkf/aAAwDAQACEQMRAD8AltJagyeH0AthI5xdrLcNM91BF5pX2HaH9bcfaSXWGaRmknyJckliyjqTzSlT54b6bk+h0R//2Q=="
          />
        ) : (
          // Fallback to default SVG if imageUrl is undefined
          <DefaultSVGBackground />
        )}

        {/* Optional overlay */}
        {overlay && (
          <div
            className="absolute inset-0 bg-black"
            style={{ opacity: overlayOpacity / 100 }}
          />
        )}
      </div>
      {/* Content Layer */}
      <div className="relative z-10 w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex flex-col items-center justify-center text-center space-y-6 py-8">
          {childrenArray.map((child, index) => (
            <div
              key={index}
              className={cn(
                "w-full flex justify-center",
                // Apply consistent spacing based on element type
                index === 0 && "mb-2", // First element (usually heading) gets less bottom margin
                index > 0 && index < childrenArray.length - 1 && "my-4", // Middle elements get more spacing
                index === childrenArray.length - 1 && "mt-6" // Last element (usually CTA) gets more top margin
              )}
            >
              {child}
            </div>
          ))}
        </div>
      </div>
      {/* Decorative Elements */}
      <div className="absolute top-0 left-0 w-full h-full pointer-events-none overflow-hidden">
        <div className="absolute -top-24 -right-24 w-64 h-64 rounded-full bg-white/5 blur-3xl" />
        <div className="absolute -bottom-24 -left-24 w-64 h-64 rounded-full bg-white/5 blur-3xl" />
      </div>{" "}
    </section>
  );
};

// Export the height type for external use
export type HeroSectionHeight = keyof typeof heightMap;

// Default export
export default HeroSection;
