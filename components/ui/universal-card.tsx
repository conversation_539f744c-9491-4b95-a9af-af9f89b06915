"use client";

import Image from "next/image";
import Link from "next/link";
import {
  Calendar,
  Clock,
  MapPin,
  Users,
  BookOpen,
  ExternalLink,
  Heart,
  Bookmark,
} from "lucide-react";
import { Button } from "./button";

// Universal Card Types
type CardData = {
  id: string;
  title: string;
  description?: string;
  excerpt?: string;
  image: string;
  category?: string;
  date?: string;
  time?: string;
  location?: string;
  author?: string;
  authorImage?: string;
  readTime?: string;
  status?: string;
  value?: string;
  icon?: string | React.ReactNode;
  href?: string;
  tags?: string[];
  color?: string;
  bgColor?: string;
  textColor?: string;
  iconBg?: string;
  highlights?: string[];
  registration?: {
    fee: string;
    deadline: string;
    url?: string;
  };
  role?: string;
  [key: string]: any;
};

interface UniversalCardProps {
  type:
    | "blog"
    | "event"
    | "program"
    | "impact"
    | "testimonial"
    | "success-story"
    | "resource";
  data: CardData;
  layout?: "grid" | "list" | "featured" | "compact";
  className?: string;
  showImage?: boolean;
  showMeta?: boolean;
}

export default function UniversalCard({
  type,
  data,
  layout = "grid",
  className = "",
  showImage = true,
  showMeta = true,
}: UniversalCardProps) {
  // Configuration based on card type
  const getCardConfig = () => {
    switch (type) {
      case "blog":
        return {
          href: `/blogs-and-news/${data.id}`,
          metadata: [
            { icon: <BookOpen className="h-3.5 w-3.5" />, text: data.readTime },
            { icon: <Calendar className="h-3.5 w-3.5" />, text: data.date },
          ],
          showAuthor: true,
          ctaText: "Read Article",
        };
      case "event":
        return {
          href: `/events/${data.id}`,
          metadata: [
            { icon: <Calendar className="h-3.5 w-3.5" />, text: data.date },
            { icon: <Clock className="h-3.5 w-3.5" />, text: data.time },
            { icon: <MapPin className="h-4 w-4" />, text: data.location },
          ],
          showAuthor: false,
          ctaText: data.status === "past" ? "View Recap" : "Learn More",
        };
      case "impact":
        return {
          href: data.href || "#",
          metadata: [],
          showAuthor: false,
          ctaText: "Learn More",
          isStatCard: true,
        };
      case "testimonial":
        return {
          href: data.href || "#",
          metadata: [],
          showAuthor: true,
          ctaText: "Read Story",
          isTestimonial: true,
        };
      case "program":
        return {
          href: `/programs/${data.id}`,
          metadata: [],
          showAuthor: false,
          ctaText: "View Program",
        };
      case "success-story":
        return {
          href: `/success-stories/${data.id}`,
          metadata: [],
          showAuthor: true,
          ctaText: "Read Story",
        };
      case "resource":
        return {
          href: data.link || "#",
          metadata: data.category
            ? [
                {
                  icon: <ExternalLink className="h-3.5 w-3.5" />,
                  text: data.category,
                },
              ]
            : [],
          showAuthor: false,
          ctaText: data.ctaText || "Access Resource",
        };
      default:
        return {
          href: "#",
          metadata: [],
          showAuthor: false,
          ctaText: "View Details",
        };
    }
  };

  const config = getCardConfig();

  // Special handling for impact stat cards
  if (config.isStatCard) {
    return (
      <div
        className={`${
          data.bgColor || "bg-white"
        } rounded-xl overflow-hidden group hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1 ${className}`}
      >
        <div className="p-8">
          <div
            className={`${data.iconBg || "bg-primary/10"} ${
              data.textColor || "text-primary"
            } w-16 h-16 rounded-full flex items-center justify-center mb-6 group-hover:scale-110 transition-transform`}
          >
            {typeof data.icon === "string" ? (
              <Image src={data.icon} alt="" width={24} height={24} />
            ) : (
              data.icon
            )}
          </div>
          <h3 className="text-4xl font-bold text-gray-900 mb-3">
            {data.value}
          </h3>
          <p
            className={`${
              data.textColor || "text-primary"
            } font-semibold text-lg mb-3`}
          >
            {data.title}
          </p>
          <p className="text-gray-700">{data.description}</p>
        </div>
        {/* Progress bar at bottom */}
        <div className="w-full h-1.5 bg-gray-100">
          <div
            className={`h-full ${
              data.textColor?.replace("text", "bg") || "bg-primary"
            } w-0 group-hover:w-full transition-all duration-1000 ease-out`}
          ></div>
        </div>
      </div>
    );
  }

  // Special handling for testimonial cards
  if (config.isTestimonial) {
    return (
      <div
        className={`bg-white rounded-xl shadow-md p-8 border border-gray-100 relative hover:shadow-lg transition-all ${className}`}
      >
        <div className="absolute top-6 right-8 text-6xl text-primary/10 font-serif">
          "
        </div>
        <p className="text-gray-700 mb-6 relative z-10 italic">
          "{data.description}"
        </p>
        {config.showAuthor && (
          <div className="flex items-center gap-4">
            <div className="w-12 h-12 rounded-full bg-gray-200 overflow-hidden">
              <Image
                src={data.authorImage || "/images/placeholder.jpg"}
                alt={data.author || "Author"}
                width={48}
                height={48}
                className="w-full h-full object-cover"
              />
            </div>
            <div>
              <p className="font-semibold text-sm">{data.author}</p>
              <p className="text-xs text-gray-600">
                {data.role || data.location}
              </p>
            </div>
          </div>
        )}
      </div>
    );
  }

  // Featured layout for prominent display
  if (layout === "featured") {
    return (
      <div
        className={`bg-white rounded-xl shadow-sm overflow-hidden border border-gray-100 hover:shadow-lg transition-all duration-300 group mb-8 ${className}`}
      >
        <div className="grid lg:grid-cols-5 h-full">
          {/* Image column - spans 2/5 */}
          {showImage && (
            <div className="relative lg:col-span-2 h-64 lg:h-auto">
              <Image
                src={data.image}
                alt={data.title}
                fill
                className="object-cover group-hover:scale-105 transition-transform duration-500"
                loading="lazy"
                onError={(e) => {
                  e.currentTarget.src = "/images/placeholder.jpg";
                }}
              />
              <div className="absolute inset-0 bg-gradient-to-r from-black/60 to-transparent lg:via-black/30 lg:to-transparent"></div>

              <div className="absolute top-4 left-4 flex gap-3">
                {data.category && (
                  <div
                    className={`${
                      data.color || "bg-primary"
                    } px-3 py-1 rounded-full text-white text-xs font-medium`}
                  >
                    {data.category}
                  </div>
                )}
                <div className="bg-white/90 backdrop-blur-sm px-3 py-1 rounded-full text-gray-800 text-xs font-medium flex items-center gap-1">
                  <svg
                    className="w-3 h-3 fill-yellow-400 text-yellow-400"
                    viewBox="0 0 24 24"
                  >
                    <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z" />
                  </svg>
                  <span>Featured</span>
                </div>
              </div>
            </div>
          )}

          {/* Content column - spans 3/5 */}
          <div className="p-8 lg:col-span-3 flex flex-col justify-between">
            <div>
              {showMeta && config.metadata.length > 0 && (
                <div className="flex items-center gap-3 mb-3">
                  {config.metadata.slice(0, 2).map((meta, index) => (
                    <span
                      key={index}
                      className="text-sm text-gray-500 flex items-center gap-1"
                    >
                      {meta.icon}
                      {meta.text}
                    </span>
                  ))}
                </div>
              )}

              <h3 className="text-2xl font-bold mb-4 text-gray-900 group-hover:text-primary transition-colors">
                {data.title}
              </h3>

              <p className="text-gray-600 mb-5">
                {data.description || data.excerpt}
              </p>

              {/* Tags */}
              {data.tags && data.tags.length > 0 && (
                <div className="flex flex-wrap gap-2 mb-6">
                  {data.tags.map((tag, index) => (
                    <span
                      key={index}
                      className={`${
                        data.bgColor || "bg-gray-100"
                      } text-gray-700 text-xs px-3 py-1 rounded-full`}
                    >
                      {tag}
                    </span>
                  ))}
                </div>
              )}
            </div>

            <div className="flex flex-wrap items-center justify-between gap-4">
              {config.showAuthor && data.author && (
                <div className="flex items-center">
                  <div className="relative w-10 h-10 rounded-full overflow-hidden border-2 border-white mr-3">
                    <Image
                      src={data.authorImage || "/images/placeholder.jpg"}
                      alt={data.author}
                      fill
                      className="object-cover"
                    />
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-900">
                      {data.author}
                    </p>
                  </div>
                </div>
              )}

              <Link
                href={config.href}
                className="bg-primary hover:bg-primary/90 text-white px-6 py-2 rounded-lg flex items-center gap-2 transition-all duration-300 group"
              >
                <span>{config.ctaText}</span>
                <svg
                  className="h-4 w-4 group-hover:translate-x-0.5 transition-transform"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M9 5l7 7-7 7"
                  />
                </svg>
              </Link>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // List layout
  if (layout === "list") {
    return (
      <div
        className={`bg-white rounded-xl overflow-hidden flex flex-col sm:flex-row border border-gray-100 hover:shadow-md transition-all duration-300 group ${className}`}
      >
        {/* Image section */}
        {showImage && (
          <div className="relative sm:w-1/3 h-52 sm:h-auto">
            <Image
              src={data.image}
              alt={data.title}
              fill
              className="object-cover group-hover:scale-105 transition-transform duration-500"
              loading="lazy"
              onError={(e) => {
                e.currentTarget.src = "/images/placeholder.jpg";
              }}
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent sm:bg-gradient-to-r sm:from-black/60 sm:via-black/20 sm:to-transparent"></div>
            {data.category && (
              <div className="absolute top-4 left-4">
                <div
                  className={`${
                    data.color || "bg-primary"
                  } px-3 py-1 rounded-full text-white text-xs font-medium`}
                >
                  {data.category}
                </div>
              </div>
            )}
            {data.status && (
              <div className="absolute bottom-4 right-4">
                <span
                  className={`${
                    data.status === "past" ? "bg-gray-500/80" : "bg-primary/80"
                  } backdrop-blur-sm text-white text-xs px-3 py-1 rounded-full`}
                >
                  {data.status === "past" ? "Past" : "Upcoming"}
                </span>
              </div>
            )}
          </div>
        )}

        {/* Content section */}
        <div className="p-6 sm:w-2/3 flex flex-col justify-between">
          <div>
            {showMeta && config.metadata.length > 0 && (
              <div className="flex items-center gap-3 mb-2 text-gray-500 text-xs">
                {config.metadata.slice(0, 2).map((meta, index) => (
                  <span key={index} className="flex items-center gap-1">
                    {meta.icon}
                    {meta.text}
                  </span>
                ))}
              </div>
            )}

            <h3 className="text-xl font-bold mb-3 text-gray-900 group-hover:text-primary transition-colors">
              {data.title}
            </h3>
            <p className="text-gray-600 mb-4 text-sm">
              {data.description || data.excerpt}
            </p>

            {config.metadata.length > 2 && (
              <div className="flex items-center gap-2 mb-4 text-gray-500 text-sm">
                {config.metadata[2].icon}
                <span>{config.metadata[2].text}</span>
              </div>
            )}
          </div>

          <div className="flex items-center justify-between pt-4 border-t border-gray-100">
            {config.showAuthor && data.author && (
              <div className="flex items-center gap-2">
                <div className="relative w-6 h-6 rounded-full overflow-hidden">
                  <Image
                    src={data.authorImage || "/images/placeholder.jpg"}
                    alt={data.author}
                    fill
                    className="object-cover"
                  />
                </div>
                <span className="text-xs font-medium text-gray-900">
                  {data.author}
                </span>
              </div>
            )}

            <Link
              href={config.href}
              className="text-primary text-sm font-medium flex items-center gap-1 group ml-auto"
            >
              <span>{config.ctaText}</span>
              <svg
                className="h-4 w-4 group-hover:translate-x-0.5 transition-transform"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M9 5l7 7-7 7"
                />
              </svg>
            </Link>
          </div>
        </div>
      </div>
    );
  }

  // Grid layout (default)
  return (
    <div
      className={`bg-white rounded-xl overflow-hidden flex flex-col h-full border border-gray-100 hover:shadow-md transition-all duration-300 group ${className}`}
    >
      {showImage && (
        <div className="relative h-52">
          <Image
            src={data.image}
            alt={data.title}
            fill
            className="object-cover group-hover:scale-105 transition-transform duration-500"
            loading="lazy"
            onError={(e) => {
              e.currentTarget.src = "/images/placeholder.jpg";
            }}
          />
          <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent"></div>
          {data.category && (
            <div className="absolute top-4 left-4">
              <div
                className={`${
                  data.color || "bg-primary"
                } px-3 py-1 rounded-full text-white text-xs font-medium`}
              >
                {data.category}
              </div>
            </div>
          )}
          {data.status && (
            <div className="absolute bottom-4 right-4">
              <span className="bg-black/30 backdrop-blur-sm text-white text-xs px-3 py-1 rounded-full">
                {data.status === "past" ? "Past" : "Upcoming"}
              </span>
            </div>
          )}
        </div>
      )}

      <div className="p-6 flex-grow flex flex-col justify-between">
        <div>
          {showMeta && config.metadata.length > 0 && (
            <div className="flex items-center gap-3 mb-2 text-gray-500 text-xs">
              {config.metadata.slice(0, 2).map((meta, index) => (
                <span key={index} className="flex items-center gap-1">
                  {meta.icon}
                  {meta.text}
                </span>
              ))}
            </div>
          )}

          <h3 className="text-xl font-bold mb-3 text-gray-900 group-hover:text-primary transition-colors">
            {data.title}
          </h3>

          {config.metadata.length > 2 && (
            <div className="flex items-center gap-2 mb-3 text-gray-500 text-sm">
              {config.metadata[2].icon}
              <span>{config.metadata[2].text}</span>
            </div>
          )}

          <p className="text-gray-600 mb-4 text-sm leading-relaxed">
            {data.description || data.excerpt}
          </p>

          {/* Tags */}
          {data.tags && data.tags.length > 0 && (
            <div className="flex flex-wrap gap-2 mb-4">
              {data.tags.slice(0, 2).map((tag, index) => (
                <span
                  key={index}
                  className={`${
                    data.bgColor || "bg-gray-100"
                  } text-gray-700 text-xs px-2 py-1 rounded-full`}
                >
                  {tag}
                </span>
              ))}
            </div>
          )}

          {/* Highlights for events */}
          {data.highlights &&
            data.highlights.length > 0 &&
            type === "event" && (
              <div className="bg-green-50 p-4 rounded-lg mb-4">
                <p className="text-sm font-medium text-green-800 mb-2">
                  Key Highlights:
                </p>
                <ul className="text-sm text-green-700 space-y-1">
                  {data.highlights.slice(0, 3).map((highlight, index) => (
                    <li key={index} className="flex items-start gap-2">
                      <span className="text-green-500 mt-1">•</span>
                      <span>{highlight}</span>
                    </li>
                  ))}
                </ul>
              </div>
            )}

          {/* Registration info for events */}
          {!data.status ||
            (data.status !== "past" &&
              data.registration &&
              type === "event" && (
                <div className="bg-gray-50 p-4 rounded-lg mb-4">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium text-gray-700">
                      Registration Fee:
                    </span>
                    <span className="text-sm font-semibold text-primary">
                      {data.registration.fee}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium text-gray-700">
                      Deadline:
                    </span>
                    <span className="text-sm text-gray-600">
                      {data.registration.deadline}
                    </span>
                  </div>
                </div>
              ))}
        </div>

        <div className="pt-4 border-t border-gray-100 flex items-center justify-between">
          {config.showAuthor && data.author && (
            <div className="flex items-center gap-2">
              <div className="relative w-6 h-6 rounded-full overflow-hidden">
                <Image
                  src={data.authorImage || "/images/placeholder.jpg"}
                  alt={data.author}
                  fill
                  className="object-cover"
                />
              </div>
              <span className="text-xs font-medium text-gray-900">
                {data.author}
              </span>
            </div>
          )}

          <Link
            href={config.href}
            className="text-primary text-sm font-medium flex items-center justify-center gap-2 py-2 px-4 border border-primary rounded-lg hover:bg-primary hover:text-white transition-all duration-300 group ml-auto"
          >
            <span>{config.ctaText}</span>
            <svg
              className="h-4 w-4 group-hover:translate-x-0.5 transition-transform"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M9 5l7 7-7 7"
              />
            </svg>
          </Link>
        </div>
      </div>
    </div>
  );
}
