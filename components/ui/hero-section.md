# HeroSection Component

A reusable, flexible hero section component for Next.js 15 applications that supports background images, default SVG graphics, and uniform content spacing.

## Features

- ✅ **Flexible Height Options**: Predefined Tailwind CSS height classes (`h-1/3`, `h-1/2`, `h-2/3`, `h-screen`)
- ✅ **Background Support**: Custom images or beautiful default SVG graphic
- ✅ **Uniform Spacing**: Automatic spacing between child elements
- ✅ **Overlay Control**: Customizable dark overlay with opacity control
- ✅ **Responsive Design**: Mobile-first responsive layout
- ✅ **TypeScript Support**: Full type safety and IntelliSense
- ✅ **Accessibility**: Semantic HTML structure
- ✅ **Performance**: Optimized images with Next.js Image component

## Installation

The component is already included in your project at `components/ui/hero-section.tsx`.

## Basic Usage

```tsx
import HeroSection from "@/components/ui/hero-section";
import { Button } from "@/components/ui/button";

export default function MyPage() {
  return (
    <HeroSection height="h-1/2" imageUrl="/path/to/image.jpg">
      <h1 className="text-4xl font-bold text-white">Heading</h1>
      <p className="text-lg text-gray-200">Paragraph text</p>
      <Button className="bg-primary text-white">Call to action</Button>
    </HeroSection>
  );
}
```

## Props

| Prop             | Type                                          | Default      | Description                                       |
| ---------------- | --------------------------------------------- | ------------ | ------------------------------------------------- |
| `imageUrl`       | `string \| null`                              | `null`       | Background image URL. If `null`, uses default SVG |
| `height`         | `"h-1/3" \| "h-1/2" \| "h-2/3" \| "h-screen"` | **Required** | Height of the hero section                        |
| `children`       | `React.ReactNode`                             | **Required** | Content to display in the hero                    |
| `className`      | `string`                                      | `undefined`  | Additional CSS classes                            |
| `overlay`        | `boolean`                                     | `true`       | Whether to show dark overlay                      |
| `overlayOpacity` | `number`                                      | `50`         | Overlay opacity (0-100)                           |

## Height Options

The component supports four predefined height options:

- **`h-1/3`**: 33.33% of viewport height - Good for page headers
- **`h-1/2`**: 50% of viewport height - Balanced option for most use cases
- **`h-2/3`**: 66.67% of viewport height - Prominent hero sections
- **`h-screen`**: 100% of viewport height - Full-screen landing pages

## Examples

### 1. Basic Hero with Default Background

```tsx
<HeroSection height="h-1/2" imageUrl={null}>
  <h1 className="text-4xl font-bold text-white">Welcome to GirlCode</h1>
  <p className="text-lg text-gray-200">Empowering women through technology</p>
  <Button className="bg-primary text-white">Get Started</Button>
</HeroSection>
```

### 2. Full-Screen Hero with Background Image

```tsx
<HeroSection height="h-screen" imageUrl="/images/hero-bg.jpg">
  <h1 className="text-6xl font-bold text-white mb-4">Transform Your Future</h1>
  <p className="text-xl text-gray-200 max-w-2xl mb-8">
    Join thousands of women empowering themselves through technology.
  </p>
  <div className="flex gap-4">
    <Button className="bg-primary text-white">Explore Programs</Button>
    <Button variant="outline" className="border-white text-white">
      Learn More
    </Button>
  </div>
</HeroSection>
```

### 3. Minimal Hero for Inner Pages

```tsx
<HeroSection height="h-1/3" imageUrl="/images/page-bg.jpg" overlayOpacity={60}>
  <h2 className="text-3xl font-bold text-white">Our Programs</h2>
  <p className="text-gray-200">Building tomorrow's leaders</p>
</HeroSection>
```

### 4. Custom Styling

```tsx
<HeroSection
  height="h-2/3"
  imageUrl={null}
  className="bg-gradient-to-br from-blue-600 to-purple-700"
  overlay={false}
>
  <h1 className="text-5xl font-bold text-white">Custom Design</h1>
  <p className="text-xl text-gray-200">With gradient background</p>
</HeroSection>
```

### 5. Complex Content Layout

```tsx
<HeroSection height="h-screen" imageUrl="/images/bootcamp-bg.jpg">
  <div className="text-center max-w-4xl">
    <div className="bg-white/10 backdrop-blur-sm rounded-full px-4 py-2 mb-6">
      <span className="text-white text-sm">🚀 New Program Launch</span>
    </div>

    <h1 className="text-6xl font-bold text-white mb-6">
      Coding Bootcamp
      <span className="block text-primary">2025</span>
    </h1>

    <p className="text-xl text-gray-200 mb-8">
      12-week intensive program for future developers
    </p>

    <div className="grid grid-cols-3 gap-6 mb-8">
      <div className="bg-white/10 rounded-lg p-4">
        <div className="text-2xl font-bold text-white">12</div>
        <div className="text-sm text-gray-300">Weeks</div>
      </div>
      <div className="bg-white/10 rounded-lg p-4">
        <div className="text-2xl font-bold text-white">50+</div>
        <div className="text-sm text-gray-300">Projects</div>
      </div>
      <div className="bg-white/10 rounded-lg p-4">
        <div className="text-2xl font-bold text-white">24/7</div>
        <div className="text-sm text-gray-300">Support</div>
      </div>
    </div>

    <Button className="bg-primary text-white text-lg px-8 py-3">
      Apply Now
    </Button>
  </div>
</HeroSection>
```

## Default SVG Background

When `imageUrl` is `null`, the component displays a beautiful default SVG with:

- Gradient background (purple to pink)
- Dot pattern overlay
- Abstract geometric shapes
- Subtle animations and effects

## Content Spacing

The component automatically applies uniform spacing between child elements:

- **First child** (typically heading): Reduced bottom margin
- **Middle children** (typically paragraphs): Consistent vertical margin
- **Last child** (typically CTA): Increased top margin

## Styling Guidelines

### Text Colors

- Use `text-white` for primary text
- Use `text-gray-200` for secondary text
- Use `text-gray-300` for tertiary text

### Buttons

- Primary actions: `bg-primary hover:bg-primary/90 text-white`
- Secondary actions: `border-white text-white hover:bg-white/20`

### Content Width

- Constrain content width with `max-w-` classes
- Use `max-w-2xl` for paragraphs
- Use `max-w-4xl` for complex layouts

## Best Practices

1. **Keep content concise** - Hero sections should be scannable
2. **Use high-quality images** - Optimize for web performance
3. **Test on mobile** - Ensure text remains readable
4. **Limit call-to-actions** - 1-2 CTAs maximum
5. **Consider accessibility** - Ensure sufficient color contrast

## Accessibility

The component includes:

- Semantic HTML structure
- Proper image alt text
- Keyboard navigation support
- Screen reader compatibility

## Browser Support

Compatible with all modern browsers that support:

- CSS Grid and Flexbox
- CSS Custom Properties
- Next.js 15 features

## Performance

- Uses Next.js `Image` component for optimized images
- Lazy loading for background images
- Minimal CSS bundle size
- No runtime JavaScript dependencies
