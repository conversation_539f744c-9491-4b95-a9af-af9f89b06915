"use client";

import Image from "next/image";
import { Button } from "@/components/ui/button";
import {
  Search,
  ChevronDown,
  ChevronRight,
  Calendar,
  Clock,
  MapPin,
} from "lucide-react";
import { useState } from "react";
import HeroSection from "@/components/ui/hero-section";
import { eventsData, eventCategories, allEventTags } from "@/data/eventsData";
import Link from "next/link";

export default function EventsHeroSection() {
  const [selectedCategory, setSelectedCategory] = useState("All");
  const [searchQuery, setSearchQuery] = useState("");

  // Featured event for header background
  const featuredEvent =
    eventsData.find((event) => event.featured) || eventsData[0];

  return (
    <div className="mt-16">
      {/* Hero Section - Enhanced */}
      <HeroSection
        imageUrl={featuredEvent.image}
        height="h-2/3"
        overlay={true}
        overlayOpacity={60}
        className="bg-gray-900 relative overflow-hidden"
      >
        {/* Enhanced accents - Fixed positioning */}
        <div className="absolute top-0 right-0 w-64 h-64 rounded-full bg-primary/10 blur-3xl opacity-50 -translate-y-1/2 translate-x-1/2"></div>
        <div className="absolute inset-x-0 bottom-0 h-32 bg-gradient-to-t from-gray-950/80 to-transparent pointer-events-none"></div>

        {/* Content with improved spacing and typography */}
        <div className="relative z-20 mx-auto max-w-7xl px-6 lg:px-8 h-full flex items-center">
          <div className="grid md:grid-cols-2 gap-12 items-center w-full py-12">
            {/* Left side - Main content */}
            <div className="flex flex-col items-start space-y-6">
              <div className="inline-flex items-center gap-2 bg-primary/20 backdrop-blur-sm py-2 px-4 rounded-full border border-primary/30">
                <span className="text-sm font-medium text-primary">
                  Our Events
                </span>
              </div>

              <h1 className="text-4xl font-bold tracking-tight text-white sm:text-5xl md:text-6xl leading-tight">
                Join Our{" "}
                <span className="text-primary bg-primary/10 px-2 rounded backdrop-blur-sm">
                  Events
                </span>
              </h1>

              <p className="text-lg text-gray-300 max-w-lg leading-relaxed">
                Discover workshops, training sessions, and networking
                opportunities designed to support women and girls in technology
                and empower communities.
              </p>

              {/* Featured tags */}
              <div className="flex flex-wrap gap-2">
                {allEventTags.slice(0, 5).map((tag) => (
                  <span
                    key={tag}
                    className="px-3 py-1.5 bg-white/10 backdrop-blur-sm text-white text-sm rounded-full hover:bg-white/20 transition-colors cursor-pointer border border-white/10"
                  >
                    {tag}
                  </span>
                ))}
                <span className="px-3 py-1.5 bg-white/5 backdrop-blur-sm text-white/70 text-sm rounded-full flex items-center border border-white/10">
                  <ChevronDown className="h-3.5 w-3.5 mr-1" />
                  More
                </span>
              </div>
            </div>

            {/* Right side - Featured event preview */}
            <div className="hidden md:block">
              <div className="bg-white/10 backdrop-blur-md border border-white/20 rounded-xl overflow-hidden hover:bg-white/15 transition-all duration-300 group shadow-2xl">
                <div className="p-6">
                  <div className="mb-4">
                    <span
                      className={`text-xs font-medium px-2.5 py-1 rounded-full ${featuredEvent.color} text-white shadow-sm`}
                    >
                      {featuredEvent.category}
                    </span>
                  </div>
                  <h2 className="text-xl font-bold text-white mb-3 group-hover:text-primary transition-colors">
                    {featuredEvent.title}
                  </h2>
                  <p className="text-gray-300 text-sm mb-4 line-clamp-3">
                    {featuredEvent.excerpt}
                  </p>

                  <div className="space-y-2 text-sm text-gray-300 mb-4">
                    <div className="flex items-center">
                      <Calendar className="w-4 h-4 mr-2 text-primary" />
                      <span>{featuredEvent.date}</span>
                    </div>
                    <div className="flex items-center">
                      <Clock className="w-4 h-4 mr-2 text-primary" />
                      <span>{featuredEvent.time}</span>
                    </div>
                    <div className="flex items-center">
                      <MapPin className="w-4 h-4 mr-2 text-primary" />
                      <span>{featuredEvent.location}</span>
                    </div>
                  </div>
                </div>
                <div className="px-6 py-3 border-t border-white/20 flex justify-between items-center bg-black/20">
                  <span className="text-gray-400 text-sm font-medium">
                    {featuredEvent.registration?.fee || "Free"}
                  </span>
                  <Button className="bg-primary hover:bg-primary/90 text-white group shadow-lg">
                    <Link
                      href={`/events/${featuredEvent.id}`}
                      className="flex items-center gap-1"
                    >
                      <span>View Event</span>
                      <ChevronRight className="h-4 w-4 group-hover:translate-x-0.5 transition-transform" />
                    </Link>
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </HeroSection>
    </div>
  );
}
