"use client";
import { useState, useEffect } from "react";
import {
  Calendar as CalendarIcon,
  Clock,
  MapPin,
  ArrowLeft,
  Calendar,
} from "lucide-react";
import Link from "next/link";
import Image from "next/image";

import { Event } from "@/data/eventsData";

export default function EventDetail({ event }: { event: Event }) {
  // This component can have all the client-side state and effects
  return (
    <div className="bg-gray-50 min-h-screen pb-16">
      {/* Back Button */}
      <div className="container mx-auto px-4 py-6">
        <Link
          href="/events"
          className="inline-flex items-center text-pink-500 hover:text-pink-600 font-medium"
        >
          <ArrowLeft className="w-4 h-4 mr-1" />
          <span>All Events</span>
        </Link>
      </div>

      {/* Hero Section */}
      <div
        className="w-full h-64 md:h-80 bg-cover bg-center relative"
        style={{
          backgroundImage: `url(${event.image})`,
          backgroundPosition: "center",
        }}
      >
        {/* Content from your original component */}
      </div>

      {/* Rest of your component */}
    </div>
  );
}
