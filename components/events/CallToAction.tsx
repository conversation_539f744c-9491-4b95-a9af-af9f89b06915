import Link from "next/link";
import { ArrowRight } from "lucide-react";

export default function CallToAction() {
  return (
    <>
      {/* Call to Action Section */}
      <div className="mt-20 bg-gradient-to-r from-pink-50 to-purple-50 rounded-xl p-8 lg:p-12 text-center">
        <h2 className="text-2xl lg:text-3xl font-bold text-gray-900 mb-4">
          Interested in hosting an event with us?
        </h2>
        <p className="text-gray-600 mb-8 max-w-2xl mx-auto">
          GirlCode is always looking for partners to collaborate on events that
          support women in technology. Together we can create impactful learning
          experiences.
        </p>
        <Link
          href="/contact"
          className="inline-block bg-pink-500 hover:bg-pink-600 text-white font-medium py-3 px-6 rounded-lg transition-colors duration-300"
        >
          Contact Us
        </Link>
      </div>
    </>
  );
}
