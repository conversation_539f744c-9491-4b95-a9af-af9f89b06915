"use client";
import { useCallback } from "react";

export default function CategoryFilters({
  value,
  onChange,
}: {
  value: string;
  onChange: (category: string) => void;
}) {
  // No need for internal state since we're controlled by parent
  const handleChange = useCallback(
    (category: string) => {
      onChange(category);
    },
    [onChange]
  );

  return (
    <div className="mb-8">
      <h3 className="font-bold text-lg mb-3">Filter by Category</h3>
      <div className="flex flex-wrap gap-2">
        <button
          className={`px-3 py-1 rounded-full text-sm ${
            value === "all"
              ? "bg-primary text-white"
              : "bg-gray-100 hover:bg-gray-200 text-gray-800"
          }`}
          onClick={() => handleChange("all")}
        >
          All Categories
        </button>
        {[
          "Workshop",
          "Webinar",
          "Hackathon",
          "Training",
          "Conference",
          "Competition",
        ].map((category) => (
          <button
            key={category}
            className={`px-3 py-1 rounded-full text-sm ${
              value === category
                ? "bg-primary text-white"
                : "bg-gray-100 hover:bg-gray-200 text-gray-800"
            }`}
            onClick={() => handleChange(category)}
          >
            {category}
          </button>
        ))}
      </div>

      <select
        value={value}
        onChange={(e) => handleChange(e.target.value)}
        className="mt-4 p-2 border rounded-md w-full md:hidden"
      >
        <option value="all">All Categories</option>
        {[
          "Workshop",
          "Webinar",
          "Hackathon",
          "Training",
          "Conference",
          "Competition",
        ].map((category) => (
          <option key={category} value={category}>
            {category}
          </option>
        ))}
      </select>
    </div>
  );
}
