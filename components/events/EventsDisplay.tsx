"use client";
import { useState, useCallback, useMemo } from "react";
import { Calendar, Clock, MapPin } from "lucide-react";
import CategoryFilters from "./CategoryFilters";
import Link from "next/link";
import { eventsData, eventCategories, Event } from "@/data/eventsData";
import EventCard from "./EventCard";

export default function EventsDisplay() {
  const [selectedCategory, setSelectedCategory] = useState("All");
  const [viewLayout, setViewLayout] = useState<"grid" | "list">("grid");

  // Filter events based on selected category
  const filteredEvents = useMemo(() => {
    if (selectedCategory === "All") {
      return eventsData;
    }
    return eventsData.filter((event) => event.category === selectedCategory);
  }, [selectedCategory]);

  // Separate upcoming and past events
  const upcomingEvents = filteredEvents.filter(
    (event) => event.status === "upcoming"
  );
  const pastEvents = filteredEvents.filter((event) => event.status === "past");

  return (
    <div className="container mx-auto max-w-7xl px-4 py-12">
      {/* Category Filter */}
      <div className="flex justify-between items-center mb-8">
        <div className="flex items-center gap-3">
          <div className="bg-primary/10 p-2 rounded-full">
            <Calendar className="text-primary w-5 h-5" />
          </div>
          <h2 className="text-2xl font-bold">Browse Events</h2>
        </div>

        <div className="flex gap-3 items-center">
          {/* View toggle buttons */}
          <div className="hidden sm:flex bg-gray-100 rounded-lg p-1">
            <button
              onClick={() => setViewLayout("grid")}
              className={`p-1.5 rounded ${
                viewLayout === "grid"
                  ? "bg-white shadow-sm"
                  : "text-gray-500 hover:text-gray-700"
              }`}
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="18"
                height="18"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <rect x="3" y="3" width="7" height="7"></rect>
                <rect x="14" y="3" width="7" height="7"></rect>
                <rect x="14" y="14" width="7" height="7"></rect>
                <rect x="3" y="14" width="7" height="7"></rect>
              </svg>
            </button>
            <button
              onClick={() => setViewLayout("list")}
              className={`p-1.5 rounded ${
                viewLayout === "list"
                  ? "bg-white shadow-sm"
                  : "text-gray-500 hover:text-gray-700"
              }`}
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="18"
                height="18"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <line x1="8" y1="6" x2="21" y2="6"></line>
                <line x1="8" y1="12" x2="21" y2="12"></line>
                <line x1="8" y1="18" x2="21" y2="18"></line>
                <line x1="3" y1="6" x2="3.01" y2="6"></line>
                <line x1="3" y1="12" x2="3.01" y2="12"></line>
                <line x1="3" y1="18" x2="3.01" y2="18"></line>
              </svg>
            </button>
          </div>
        </div>
      </div>

      {/* Category Filters */}
      <div className="mb-8">
        <div className="flex flex-wrap gap-2">
          <button
            onClick={() => setSelectedCategory("All")}
            className={`px-4 py-2 rounded-full text-sm font-medium transition-colors ${
              selectedCategory === "All"
                ? "bg-primary text-white"
                : "bg-gray-100 text-gray-800 hover:bg-gray-200"
            }`}
          >
            All Events
          </button>
          {eventCategories.slice(1).map((category) => (
            <button
              key={category}
              onClick={() => setSelectedCategory(category)}
              className={`px-4 py-2 rounded-full text-sm font-medium transition-colors ${
                selectedCategory === category
                  ? "bg-primary text-white"
                  : "bg-gray-100 text-gray-800 hover:bg-gray-200"
              }`}
            >
              {category}
            </button>
          ))}
        </div>
      </div>

      {/* Upcoming Events */}
      {upcomingEvents.length > 0 && (
        <div className="mb-12">
          <h3 className="text-xl font-bold mb-6 text-gray-900">
            Upcoming Events ({upcomingEvents.length})
          </h3>
          <div
            className={
              viewLayout === "grid"
                ? "grid md:grid-cols-2 lg:grid-cols-3 gap-6"
                : "space-y-6"
            }
          >
            {upcomingEvents.map((event) => (
              <EventCard key={event.id} event={event} layout={viewLayout} />
            ))}
          </div>
        </div>
      )}

      {/* Past Events */}
      {pastEvents.length > 0 && (
        <div>
          <h3 className="text-xl font-bold mb-6 text-gray-900">
            Past Events ({pastEvents.length})
          </h3>
          <div
            className={
              viewLayout === "grid"
                ? "grid md:grid-cols-2 lg:grid-cols-3 gap-6"
                : "space-y-6"
            }
          >
            {pastEvents.map((event) => (
              <EventCard key={event.id} event={event} layout={viewLayout} />
            ))}
          </div>
        </div>
      )}

      {/* No events message */}
      {filteredEvents.length === 0 && (
        <div className="text-center py-12">
          <Calendar className="w-16 h-16 text-gray-300 mx-auto mb-4" />
          <h3 className="text-xl font-medium text-gray-900 mb-2">
            No events found
          </h3>{" "}
          <p className="text-gray-600">
            Try selecting a different category or check back later for new
            events.
          </p>
        </div>
      )}
    </div>
  );
}
