"use client";
import UniversalCard from "../ui/universal-card";

interface Event {
  id: string;
  title: string;
  date: string;
  time: string;
  location: string;
  description: string;
  image: string;
  category: string;
  status: "upcoming" | "past";
  registration?: {
    fee: string;
    deadline: string;
    url?: string;
  };
  highlights?: string[];
}

const EventCard = ({
  event,
  layout = "grid",
}: {
  event: Event;
  layout?: "grid" | "list";
}) => {
  // Transform event data to match UniversalCard format
  const cardData = {
    id: event.id,
    title: event.title,
    description: event.description,
    image: event.image,
    category: event.category,
    date: event.date,
    time: event.time,
    location: event.location,
    status: event.status,
    highlights: event.highlights,
    registration: event.registration,
    color: event.status === "past" ? "bg-gray-500" : "bg-primary",
  };

  return (
    <UniversalCard
      type="event"
      data={cardData}
      layout={layout}
      showImage={true}
      showMeta={true}
    />
  );
};

export default EventCard;
