"use client";

import Link from "next/link";
import { ChevronRight } from "lucide-react";
import Image from "next/image";
import { But<PERSON> } from "@/components/ui/button";

export default function SDGFocusAreas() {
  const sdgs = [
    {
      number: 4,
      title: "Quality Education",
      description:
        "We promote inclusive and equitable quality education, providing technology skills training and digital literacy to create lifelong learning opportunities.",
      initiatives: [
        "Digital literacy programs",
        "Coding boot camps",
        "Tech scholarships",
      ],
      icon: "/vectors/books.svg",
      link: "/programs/sdg-4",
      color: "border-blue-400",
      textColor: "text-blue-700",
      accentColor: "bg-blue-500",
    },
    {
      number: 5,
      title: "Gender Equality",
      description:
        "Our initiatives support women's empowerment through technology, closing the digital gender gap and creating leadership opportunities for young women.",
      initiatives: [
        "Women in tech mentorship",
        "Leadership development",
        "Tech career pathways",
      ],
      icon: "/vectors/rainbow.svg",
      link: "/programs/sdg-5",
      color: "border-pink-400",
      textColor: "text-pink-700",
      accentColor: "bg-pink-500",
    },
    {
      number: 3,
      title: "Good Health & Well-Being",
      description:
        "Ensure healthy lives and promote well-being for all at all ages through technology and community support.",
      initiatives: [
        "Mental Health for Thriving Futures",
        "Teen Self-Help Handbook",
        "Peer Support Networks",
      ],
      impact:
        "Distributed 15,000+ copies of the Teen Self-Help Handbook, reaching girls in 18 countries.",
      icon: "/vectors/books.svg",
      link: "/programs/sdg-3",
      color: "border-purple-400",
      textColor: "text-purple-700",
      accentColor: "bg-purple-500",
    },
    {
      number: 13,
      title: "Climate Action",
      description:
        "We engage youth in developing tech-based climate solutions, fostering innovation that addresses environmental challenges in their communities.",
      initiatives: [
        "Climate hackathons",
        "Green tech innovation",
        "Environmental data projects",
      ],
      icon: "/vectors/cloud-sun.svg",
      link: "/programs/sdg-13",
      color: "border-green-400",
      textColor: "text-green-700",
      accentColor: "bg-green-500",
    },
  ];

  return (
    <section className="bg-gray-50 py-20">
      <div className="container mx-auto max-w-screen-xl px-6 lg:px-8">
        {/* Section Header */}
        <div className="max-w-3xl mx-auto text-center mb-16">
          <div className="h-1 w-12 bg-primary mx-auto mb-6"></div>
          <h2 className="font-semibold text-3xl lg:text-4xl text-gray-800 mb-4">
            Our SDG Focus Areas
          </h2>
          <p className="text-gray-600 leading-relaxed">
            Through our programs, we contribute to key UN Sustainable
            Development Goals, creating meaningful impact for girls and youth
            worldwide.
          </p>
        </div>

        {/* SDG Cards */}
        <div className="space-y-8">
          {sdgs.map((sdg, index) => (
            <div
              key={sdg.number}
              className="bg-white shadow-sm border border-gray-100 rounded-lg overflow-hidden"
            >
              <div
                className={`flex flex-col ${
                  index % 2 !== 0 ? "lg:flex-row-reverse" : "lg:flex-row"
                } items-stretch`}
              >
                {/* Icon Column */}
                <div className="lg:w-1/6 bg-gray-50 flex items-center justify-center p-8">
                  <div className="flex flex-col items-center space-y-4">
                    <div
                      className={`${sdg.color} border-2 rounded-full p-2 w-16 h-16 flex items-center justify-center`}
                    >
                      <span className="text-gray-700 font-semibold text-xl">
                        {sdg.number}
                      </span>
                    </div>
                    <Image
                      src={sdg.icon}
                      alt={sdg.title}
                      width={48}
                      height={48}
                      className="opacity-70"
                    />
                  </div>
                </div>

                {/* Content Column */}
                <div className="lg:w-5/6 p-8">
                  <div className="flex flex-col h-full">
                    <div className="mb-4">
                      <h3 className="text-xl font-semibold text-gray-800 mb-1">
                        {sdg.title}
                      </h3>
                      <div className={`h-1 w-12 ${sdg.accentColor}`}></div>
                    </div>

                    <p className="text-gray-600 mb-6">{sdg.description}</p>

                    {sdg.impact && (
                      <div className="mb-6 bg-gray-50 border border-gray-200  p-4">
                        <p className="text-sm text-gray-800">
                          <span className="font-semibold">Impact: </span>
                          {sdg.impact}
                        </p>
                      </div>
                    )}

                    <div className="mb-6">
                      <p className="font-medium text-gray-700 mb-3">
                        Key Initiatives:
                      </p>
                      <ul className="space-y-2">
                        {sdg.initiatives.map((initiative, i) => (
                          <li
                            key={i}
                            className="flex items-start gap-3 text-gray-600"
                          >
                            <div
                              className="mt-1.5 h-1.5 w-1.5 rounded-full flex-shrink-0"
                              style={{
                                backgroundColor: getComputedColor(
                                  sdg.accentColor
                                ),
                              }}
                            ></div>
                            <span>{initiative}</span>
                          </li>
                        ))}
                      </ul>
                    </div>

                    <div className="mt-auto">
                      <Link
                        href={sdg.link}
                        className={`inline-flex items-center text-sm font-medium ${sdg.textColor} hover:underline`}
                      >
                        Learn more
                        <ChevronRight className="ml-1 h-4 w-4" />
                      </Link>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* View All Programs Button */}
        <div className="flex justify-center mt-12">
          <Link href="/programs">
            <Button className="bg-gray-800 hover:bg-gray-700 text-white ">
              <span className="flex items-center gap-1">
                View All Our Programs
                <ChevronRight className="h-4 w-4" />
              </span>
            </Button>
          </Link>
        </div>
      </div>
    </section>
  );
}

// Helper function to convert class name to computed color (simulation for the component)
interface ColorMap {
  [key: string]: string;
}

function getComputedColor(className: string): string {
  const colorMap: ColorMap = {
    "bg-blue-500": "#3b82f6",
    "bg-pink-500": "#ec4899",
    "bg-purple-500": "#8b5cf6",
    "bg-green-500": "#22c55e",
  };

  return colorMap[className] || "#6b7280";
}
