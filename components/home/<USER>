import Image from "next/image";
import { But<PERSON> } from "@/components/ui/button";

export default function NewsletterSection() {
  return (
    <div className="w-full h-auto flex flex-col md:flex-row gap-12 py-16 px-5 items-center xl:py-28 xl:px-16">
      <div className="w-full h-auto flex flex-col gap-5 xl:w-1/2 xl:gap-6">
        <h2 className="font-bold text-3xl xl:text-5xl">
          Stay Updated with Girl<span className="text-primary">Code</span>
        </h2>

        <p className="text-base md:text-lg">
          Join our community and receive the latest news, updates, and success
          stories from GirlCode.
        </p>

        <div className="w-full flex flex-col gap-4 mt-4">
          <input
            type="email"
            placeholder="Your Email Here"
            className="w-full p-4 border border-gray-300 "
          />

          <Button className="w-full md:w-32 bg-primary hover:bg-primary/90">
            Sign Up
          </Button>

          <p className="text-sm text-gray-600">
            By clicking Sign Up, you agree to our Terms and Conditions.
          </p>
        </div>
      </div>

      <div className="w-full xl:w-1/2">
        <Image
          src={"/images/girlcode-community.jpg"}
          alt="GirlCode Community"
          width={800}
          height={600}
          className=" w-full"
        />
      </div>
    </div>
  );
}
