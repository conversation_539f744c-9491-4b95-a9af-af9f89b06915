"use client";

import Link from "next/link";
import Image from "next/image";
import { ChevronRight } from "lucide-react";
import HeroSection from "@/components/ui/hero-section";

export default function HomeHeroSection() {
  return (
    <div className="mt-16">
      <HeroSection
        height="h-2/3"
        className="bg-white text-gray-900"
        overlay={false}
      >
        <div className="container mx-auto flex flex-col md:flex-row items-start gap-16 px-6 lg:px-8 py-8">
          {/* Text Section */}
          <div className="flex flex-col w-full md:w-1/2 space-y-6 text-left">
            <div className="h-1 w-12 bg-primary mb-2"></div>
            <h1 className="text-3xl md:text-4xl lg:text-5xl font-semibold tracking-tight text-gray-800">
              Girl<span className="text-primary">Code</span>
            </h1>
            <h2 className="text-2xl md:text-3xl font-light leading-snug text-gray-700">
              Empowering Women & Youth Through Technology
            </h2>
            <p className="text-base md:text-lg text-gray-600 max-w-lg leading-relaxed">
              We equip women and youth with the tools, community, and platforms
              they need to break barriers, achieve financial independence, and
              drive systemic change.
            </p>
            <div className="pt-4 flex flex-col sm:flex-row gap-4">
              <Link
                href="/programs"
                className="inline-flex items-center justify-center px-6 py-3 bg-gray-800 text-white hover:bg-gray-700 transition group"
              >
                <span>Explore Programs</span>
                <ChevronRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
              </Link>
              <Link
                href="/get-involved"
                className="inline-flex items-center justify-center px-6 py-3 border border-gray-300 text-gray-700 hover:bg-gray-50 transition"
              >
                <span>Get Involved</span>
              </Link>
            </div>
          </div>

          {/* Image Section */}
          <div className="w-full md:w-1/2">
            <div className="relative">
              {/* Main image */}
              <div className="relative rounded-lg overflow-hidden shadow-md">
                <div className="w-full h-[320px] md:h-[380px] lg:h-[420px]">
                  <Image
                    src="/images/gia-1.jpg"
                    alt="Women learning technology skills in a collaborative environment"
                    fill
                    className="object-cover"
                    priority
                    quality={85}
                    sizes="(max-width: 768px) 100vw, 50vw"
                    placeholder="blur"
                    blurDataURL="data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAAIAAoDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAhEAACAQMDBQAAAAAAAAAAAAABAgMABAUGIWGRkrHB0f/EABUBAQEAAAAAAAAAAAAAAAAAAAMF/8QAGhEAAgIDAAAAAAAAAAAAAAAAAAECEgMRkf/aAAwDAQACEQMRAD8AltJagyeH0AthI5xdrLcNM91BF5pX2HaH9bcfaSXWGaRmknyJckliyjqTzSlT54b6bk+h0R//2Q=="
                  />
                </div>
                {/* Subtle overlay gradient */}
                <div className="absolute inset-0 bg-gradient-to-r from-black/20 via-transparent to-transparent"></div>
              </div>

              {/* Secondary image - subtle overlap */}
              <div className="absolute -bottom-6 -right-6 w-48 h-48 md:w-64 md:h-64 rounded-lg overflow-hidden shadow-lg border-4 border-white hidden sm:block">
                <Image
                  src="/images/empowering-image.JPG"
                  alt="Empowering women through technology education and mentorship"
                  fill
                  className="object-cover"
                  quality={80}
                  sizes="(max-width: 768px) 192px, 256px"
                  loading="eager"
                  placeholder="blur"
                  blurDataURL="data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAAIAAoDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAhEAACAQMDBQAAAAAAAAAAAAABAgMABAUGIWGRkrHB0f/EABUBAQEAAAAAAAAAAAAAAAAAAAMF/8QAGhEAAgIDAAAAAAAAAAAAAAAAAAECEgMRkf/aAAwDAQACEQMRAD8AltJagyeH0AthI5xdrLcNM91BF5pX2HaH9bcfaSXWGaRmknyJckliyjqTzSlT54b6bk+h0R//2Q=="
                />
              </div>

              {/* Primary color accent element */}
              <div className="absolute -left-3 top-1/2 transform -translate-y-1/2 h-20 w-1.5 bg-primary rounded-full hidden md:block"></div>
            </div>
          </div>
        </div>
      </HeroSection>
    </div>
  );
}
