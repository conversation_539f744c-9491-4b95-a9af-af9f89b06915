"use client";

import { <PERSON>, ChevronRight } from "lucide-react";
import { But<PERSON> } from "../ui/button";

// Impact Section Component
export default function ImpactSection() {
  return (
    <div className="w-full relative py-24 px-5 md:py-32 xl:py-40 text-white overflow-hidden">
      {/* Background image with overlay */}
      <div className="absolute inset-0">
        <div className="absolute inset-0 bg-[url('/images/image-3.jpg')] bg-cover bg-center bg-fixed bg-no-repeat"></div>
        <div className="absolute inset-0 bg-gradient-to-r from-black/70 to-black/50"></div>
        {/* Decorative elements */}
        <div className="absolute bottom-0 left-0 w-full h-16 bg-gradient-to-t from-black/30 to-transparent"></div>
      </div>

      {/* Content positioned above the background */}
      <div className="relative z-10 max-w-7xl mx-auto">
        <div className="flex flex-col md:flex-row gap-12 md:gap-16 lg:gap-24">
          {/* Text content */}
          <div className="flex flex-col gap-6 md:w-1/2">
            <div className="inline-flex items-center gap-2 bg-white/10 backdrop-blur-sm py-1 px-3 rounded-full w-fit">
              <div className="w-2 h-2 bg-primary rounded-full animate-pulse"></div>
              <p className="text-sm font-medium">Measurable Impact</p>
            </div>

            <div className="flex flex-col gap-6">
              <h2 className="font-bold text-4xl md:text-5xl xl:text-6xl leading-tight">
                Our Achievements in{" "}
                <span className="text-primary">Empowering</span> Young Women
              </h2>

              <p className="text-base xl:text-lg text-gray-100">
                At GirlCode, we track our impact across multiple dimensions to
                ensure our programs create meaningful change. Our focus on
                education, technology, and leadership has equipped thousands of
                girls with the skills they need to thrive in the digital
                economy.
              </p>

              <div className="flex flex-col sm:flex-row gap-4 mt-2">
                <div className="flex flex-col sm:flex-row gap-4 mt-2">
                  {/* First Button */}
                  <Button
                    variant="outline"
                    className="border-white text-white bg-transparent hover:bg-white hover:text-black w-full sm:w-auto "
                    onClick={() => {
                      // Add your navigation logic here
                      window.location.href = "/impact";
                    }}
                  >
                    View Impact Report
                  </Button>

                  {/* Second Button */}
                  {/* <Button
                    variant="ghost"
                    className="text-white bg-transparent hover:bg-white/10 w-full sm:w-auto  flex flex-row gap-2 items-center"
                    onClick={() => {
                      // Add your navigation logic here
                      window.location.href = "/";
                    }}
                  >
                    <span>Our Stories</span>
                    <ChevronRight className="h-4 w-4" />
                  </Button> */}
                </div>
              </div>
            </div>
          </div>

          {/* Stats */}
          <div className="md:w-1/2">
            <div className="backdrop-blur-sm bg-black/20 rounded-xl p-8 border border-white/10">
              <div className="grid grid-cols-1 gap-8 md:gap-6">
                {/* Stat 1 */}
                <div className="relative">
                  <div className="absolute -left-5 top-1/2 -translate-y-1/2 w-2 h-16 bg-primary rounded-full"></div>
                  <div className="pl-4 flex items-center gap-6">
                    <div>
                      <div className="flex items-baseline gap-1">
                        <h3 className="font-bold text-6xl md:text-7xl">
                          2,000
                        </h3>
                        <span className="text-3xl font-bold">+</span>
                      </div>
                      <p className="text-lg text-gray-200">Girls Trained</p>
                    </div>
                    <div className="hidden md:block h-16 w-px bg-white/20"></div>
                    <p className="hidden md:block text-sm text-gray-300 max-w-[180px]">
                      Across 25+ communities, with a focus on underserved areas
                    </p>
                  </div>
                </div>

                {/* Stat 2 */}
                <div className="relative">
                  <div className="absolute -left-5 top-1/2 -translate-y-1/2 w-2 h-16 bg-green-400 rounded-full"></div>
                  <div className="pl-4 flex items-center gap-6">
                    <div>
                      <div className="flex items-baseline">
                        <h3 className="font-bold text-6xl md:text-7xl">86</h3>
                        <span className="text-3xl font-bold">%</span>
                      </div>
                      <p className="text-lg text-gray-200">
                        Program Completion
                      </p>
                    </div>
                    <div className="hidden md:block h-16 w-px bg-white/20"></div>
                    <p className="hidden md:block text-sm text-gray-300 max-w-[180px]">
                      Consistently high engagement despite socioeconomic
                      challenges
                    </p>
                  </div>
                </div>

                {/* Stat 3 */}
                <div className="relative">
                  <div className="absolute -left-5 top-1/2 -translate-y-1/2 w-2 h-16 bg-blue-400 rounded-full"></div>
                  <div className="pl-4 flex items-center gap-6">
                    <div>
                      <div className="flex items-baseline">
                        <h3 className="font-bold text-6xl md:text-7xl">72</h3>
                        <span className="text-3xl font-bold">%</span>
                      </div>
                      <p className="text-lg text-gray-200">
                        Tech Career Pathway
                      </p>
                    </div>
                    <div className="hidden md:block h-16 w-px bg-white/20"></div>
                    <p className="hidden md:block text-sm text-gray-300 max-w-[180px]">
                      Pursuing further education or employment in technology
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
