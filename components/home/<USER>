"use client";

import Image from "next/image";
import Link from "next/link";
import { ChevronRight } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";

export default function CallToActionSection() {
  return (
    <section className="bg-white py-20">
      <div className="container mx-auto max-w-screen-xl px-6 lg:px-8 shadow-sm">
        <div className="bg-gray-50 rounded-lg overflow-hidden border border-gray-100 shadow-sm">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-0">
            {/* Image side */}
            <div className="relative">
              <div className="h-full min-h-[300px] lg:min-h-[400px] relative">
                <Image
                  src="/images/certificate-image.jpg"
                  alt="Students collaborating"
                  fill
                  className="object-cover"
                />

                {/* Subtle overlay */}
                <div className="absolute inset-0 bg-black/10"></div>
              </div>
            </div>

            {/* Content side */}
            <div className="p-8 md:p-10 lg:p-16 flex flex-col justify-center">
              <div className="h-1 w-12 bg-primary mb-6"></div>

              <h2 className="font-semibold text-3xl md:text-4xl text-gray-800 mb-4">
                Join the Technology Revolution
              </h2>

              <p className="text-gray-600 mb-8 max-w-lg leading-relaxed">
                Empower the next generation and make a lasting impact in your
                community. Together, we can create opportunities for young women
                in technology.
              </p>

              <div className="flex flex-col sm:flex-row gap-4">
                <Link href="/get-involved" className="flex items-center gap-1">
                  <Button className="bg-gray-800 hover:bg-gray-700 text-white ">
                    Get Involved
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                </Link>

                <Link href="/about-us">
                  <Button
                    variant="outline"
                    className="border-gray-300 text-gray-700 hover:bg-gray-50 "
                  >
                    Learn More
                  </Button>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
