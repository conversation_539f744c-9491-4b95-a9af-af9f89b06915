"use client";

import Image from "next/image";
import Link from "next/link";
import { ChevronRight } from "lucide-react";

export default function SuccessStorySection() {
  return (
    <section className="bg-gray-50 py-20">
      <div className="container mx-auto max-w-screen-xl px-6 lg:px-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
          {/* Left Column - Content */}
          <div className="flex flex-col justify-center">
            <div className="h-1 w-12 bg-primary mb-6"></div>
            <h2 className="font-semibold text-3xl lg:text-4xl text-gray-800 mb-6">
              From First-Time Coder to Tech Entrepreneur
            </h2>

            <p className="text-gray-600 leading-relaxed mb-6">
              <PERSON><PERSON> went from never having used a computer to launching her own
              web development company within 18 months of joining GirlCode. Her
              business now employs five other program graduates and provides
              tech services to local businesses in her community.
            </p>

            <p className="text-gray-600 italic border-l-2 border-gray-200 pl-4 mb-8">
              "GirlCode didn't just teach me to code – they showed me I belong
              in the tech industry and gave me the confidence to create
              opportunities for others."
            </p>

            <div className="flex items-center gap-6 mb-8">
              <div className="w-12 h-12 rounded-full overflow-hidden border border-gray-200">
                <Image
                  src="/images/profile-9.png"
                  alt="Thandi's portrait"
                  width={100}
                  height={100}
                  className="w-full h-full object-cover"
                />
              </div>
              <div>
                <p className="font-medium text-gray-800">Thandi Nkosi</p>
                <p className="text-gray-600 text-sm">
                  Founder, WebSolutions Africa
                </p>
              </div>
            </div>

            <div className="flex space-x-6 mt-4">
              <Link
                href="/blogs-and-news"
                className="inline-flex items-center justify-center px-6 py-3 bg-gray-800 text-white hover:bg-gray-700  transition group"
              >
                <span>Read More Stories</span>
                <ChevronRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
              </Link>

              <Link
                href="/programs"
                className="inline-flex items-center justify-center px-6 py-3 border border-gray-300 text-gray-700 hover:bg-gray-50  transition"
              >
                <span>Explore Programs</span>
              </Link>
            </div>
          </div>

          {/* Right Column - Timeline and Image */}
          <div className="relative">
            {/* Main Image */}
            <div className="relative rounded-lg overflow-hidden shadow-md border border-gray-100">
              <div className="aspect-[4/3]">
                <Image
                  src="/images/gc-13.jpg"
                  alt="Thandi's journey"
                  fill
                  className="object-cover"
                />
                {/* Subtle overlay gradient */}
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent"></div>
              </div>
            </div>

            {/* Success Timeline */}
            <div className="absolute -bottom-12 -left-6 right-6 bg-white p-8 rounded-lg shadow-md border border-gray-100">
              <h3 className="text-gray-700 font-medium mb-6">
                Success Timeline
              </h3>

              <div className="relative pl-8 pb-1">
                <div className="absolute top-0 left-0 h-full w-px bg-gray-200"></div>

                {timelineItems.map((item, index) => (
                  <div key={index} className="relative mb-6 last:mb-0">
                    <div className="absolute -left-8 mt-1.5 w-4 h-4 rounded-full border-2 border-primary flex items-center justify-center bg-white">
                      <div className="w-1.5 h-1.5 bg-primary rounded-full"></div>
                    </div>

                    <div>
                      <p className="font-medium text-gray-800">{item.date}</p>
                      <p className="text-gray-600">{item.event}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Accent element */}
            <div className="absolute -top-6 -right-6 w-48 h-48 bg-gray-100 rounded-lg -z-10"></div>
          </div>
        </div>
      </div>
    </section>
  );
}

// Timeline data
const timelineItems = [
  {
    date: "January 2021",
    event: "Joined GirlCode's training program",
  },
  {
    date: "November 2021",
    event: "Completed advanced web development course",
  },
  {
    date: "May 2022",
    event: "Secured first client project",
  },
  {
    date: "January 2023",
    event: "Founded WebSolutions Africa",
  },
];
