"use client";

import { ChevronRight } from "lucide-react";
import Image from "next/image";
import Link from "next/link";

export default function EmpoweringSection() {
  return (
    <section className="bg-white py-20">
      <div className="container mx-auto max-w-screen-xl px-6 lg:px-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16">
          {/* Content Column */}
          <div className="flex flex-col justify-center">
            <div className="h-1 w-12 bg-primary mb-6"></div>
            <h2 className="font-semibold text-3xl lg:text-4xl leading-tight mb-8 text-gray-800">
              Empowering Women & Youth to Transform Tomorrow
            </h2>

            <p className="text-gray-600 mb-6 leading-relaxed">
              At GirlCode, we're more than a movement—we're a revolution. We
              believe in a world where women and youth don't just survive but
              thrive, rewriting the rules of equality, innovation, and
              opportunity. Through education, advocacy, and sustainable action,
              we're turning potential into power, one skill and one voice at a
              time.
            </p>

            <p className="text-gray-600 mb-8 leading-relaxed">
              We equip women and youth with the tools, community, and platforms
              they need to break barriers, achieve financial independence, and
              drive systemic change. Our work aligns with the United Nations
              Sustainable Development Goals, creating impact that resonates
              globally and transforms locally.
            </p>

            <h3 className="font-semibold text-xl text-gray-800 mb-4">
              Our Key Initiatives
            </h3>

            <ul className="space-y-3 mb-8">
              {[
                "Tech Skills for Financial Freedom",
                "Ending SGBV, Empowering Survivors",
                "Sustainable Manufacturing & Climate Action",
                "Mental Health for Thriving Futures",
              ].map((item, index) => (
                <li
                  key={index}
                  className="flex items-start gap-3 text-gray-600"
                >
                  <div className="mt-1 h-4 w-4 rounded-full border border-primary flex items-center justify-center">
                    <div className="h-2 w-2 rounded-full bg-primary"></div>
                  </div>
                  {item}
                </li>
              ))}
            </ul>

            <div className="flex flex-col sm:flex-row gap-6 mt-4">
              <Link
                href="/about-us"
                className="inline-flex items-center justify-center px-6 py-3 bg-gray-800 text-white hover:bg-gray-700  transition group"
              >
                <span>About Us</span>
                <ChevronRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
              </Link>

              <Link
                href="/programs"
                className="inline-flex items-center justify-center px-6 py-3 border border-gray-300 text-gray-700 hover:bg-gray-50  transition"
              >
                <span>Explore Programs</span>
              </Link>
            </div>
          </div>

          {/* Image Column */}
          <div className="relative">
            {/* Vision Statement Card */}
            <div className="absolute top-0 right-0 bg-white w-4/5 md:w-3/4 shadow-lg z-10 rounded-lg overflow-hidden">
              <div className="border-l-4 border-primary p-8">
                <span className="text-sm text-gray-500 uppercase tracking-wider font-medium mb-3 block">
                  Our Vision
                </span>
                <h3 className="text-gray-800 font-semibold text-xl md:text-2xl leading-tight">
                  A future where no dream is limited by gender, poverty, or
                  circumstance.
                </h3>
              </div>
            </div>

            {/* Main Image */}
            <div className="mt-20 rounded-lg overflow-hidden shadow-md border border-gray-100">
              <div className="relative h-[400px] lg:h-[480px]">
                <Image
                  src="/images/gc-16.jpg"
                  // src="/images/education-image.png"
                  alt="Young women learning technology skills"
                  fill
                  className="object-cover"
                />
              </div>
            </div>

            {/* Subtle accent element */}
            <div className="absolute -bottom-4 -left-4 h-24 w-2 bg-primary rounded-full hidden lg:block"></div>

            {/* Background pattern */}
            <div className="absolute -bottom-8 -right-8 w-64 h-64 bg-gray-50 rounded-lg -z-10 hidden lg:block"></div>
          </div>
        </div>
      </div>
    </section>
  );
}
