"use client";

import Image from "next/image";

// Simple interface for partner data
interface Partner {
  id: number;
  logo: string;
  name: string; // For alt text only
}

export default function PartnersSection() {
  // Sample partner data - replace with actual partners
  const partners: Partner[] = [
    { id: 1, name: "Partner 1", logo: "/partners/barbah.jpg" },
    { id: 2, name: "Partner 2", logo: "/partners/giaa.jpg" },
    { id: 3, name: "Partner 3", logo: "/partners/girlup.jpg" },
    { id: 4, name: "Partner 4", logo: "/partners/inxs.png" },
    { id: 5, name: "Partner 5", logo: "/partners/moguls.jpg" },
    { id: 6, name: "Partner 6", logo: "/partners/onala.jpg" },
  ];

  return (
    <section className="bg-white py-16 mt-12">
      <div className="container mx-auto max-w-screen-xl px-6">
        {/* Section Title */}
        <h2 className="text-center text-3xl font-semibold text-gray-800 mb-12">
          Our Partners
        </h2>

        {/* Logo Grid */}
        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-8">
          {partners.map((partner) => (
            <div
              key={partner.id}
              className="bg-white rounded flex items-center justify-center h-24 transition-all hover:shadow-sm"
            >
              <Image
                src={partner.logo}
                alt={partner.name}
                width={120}
                height={60}
                className="max-h-16 w-auto object-contain"
              />
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
