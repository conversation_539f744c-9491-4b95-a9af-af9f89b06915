import { NavDropDownCardItem } from "@/types";
import Image from "next/image";
import Link from "next/link";

export const NavDropDownGridCard = ({
	item
}: {
	item: NavDropDownCardItem;
}) => {
	return (
		<Link href={item.id} className="flex space-x-2 relative z-10">
			<Image
				src={item.imgUrl}
				width={140}
				height={70}
				alt={item.title}
				className="flex-shrink-0 shadow-2xl object-cover"
				priority
			/>
			<div>
				<h4 className="text-xl font-bold mb-1 text-black dark:text-white max-w-[12rem] line-clamp-2">
					{item.title}
				</h4>
				<p className="text-neutral-700 text-sm max-w-[10rem] dark:text-neutral-300 line-clamp-4">
					{item.description}
				</p>
			</div>
		</Link>
	);
};
