import { useTheme } from "next-themes";
import React, { useEffect, useState } from "react";
import { motion } from "motion/react";

const ThemeSwitcher = () => {
  const [mounted, setMounted] = useState(false);
  const { setTheme, resolvedTheme } = useTheme();

  useEffect(() => setMounted(true), []);

  if (!mounted) {
    return (
      <div className="size-6 animate-pulse bg-gray-200 dark:bg-gray-700 rounded-full" />
    );
  }

  const raysVariants = {
    hidden: {
      strokeOpacity: 0,
      transition: {
        staggerChildren: 0.02,
        staggerDirection: -1,
        duration: 0.3,
      },
    },
    visible: {
      strokeOpacity: 1,
      transition: {
        staggerChildren: 0.02,
        duration: 0.3,
      },
    },
  };

  const rayVariant = {
    hidden: {
      pathLength: 0,
      opacity: 0,
      scale: 0,
    },
    visible: {
      pathLength: 1,
      opacity: 1,
      scale: 1,
      transition: {
        duration: 0.3,
        ease: "easeOut",
      },
    },
  };

  const sunPath =
    "M70 49.5C70 60.8218 60.8218 70 49.5 70C38.1782 70 29 60.8218 29 49.5C29 38.1782 38.1782 29 49.5 29C60 29 69.5 38 70 49.5Z";
  const moonPath =
    "M70 49.5C70 60.8218 60.8218 70 49.5 70C38.1782 70 29 60.8218 29 49.5C29 38.1782 38.1782 29 49.5 29C39 45 49.5 59.5 70 49.5Z";

  const isDark = resolvedTheme === "dark";

  return (
    <button
      onClick={() => setTheme(isDark ? "light" : "dark")}
      aria-label={`Switch to ${isDark ? "light" : "dark"} mode`}
      className="focus:outline-none active:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:ring-pink-500 rounded-full p-1"
    >
      <motion.svg
        strokeWidth="4"
        strokeLinecap="round"
        width={100}
        height={100}
        viewBox="0 0 100 100"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        className="size-5 "
      >
        <motion.g
          variants={raysVariants}
          initial="hidden"
          animate={isDark ? "visible" : "hidden"}
          className="stroke-yellow-400"
          style={{ strokeLinecap: "round", strokeWidth: 6 }}
        >
          <motion.path
            className="origin-center"
            variants={rayVariant}
            d="M50 2V11"
          />
          <motion.path variants={rayVariant} d="M85 15L78 22" />
          <motion.path variants={rayVariant} d="M98 50H89" />
          <motion.path variants={rayVariant} d="M85 85L78 78" />
          <motion.path variants={rayVariant} d="M50 98V89" />
          <motion.path variants={rayVariant} d="M23 78L16 84" />
          <motion.path variants={rayVariant} d="M11 50H2" />
          <motion.path variants={rayVariant} d="M23 23L16 16" />
        </motion.g>

        <motion.path
          d={isDark ? sunPath : moonPath}
          fill={isDark ? "#eab308" : "#60a5fa"}
          stroke={isDark ? "#eab308" : "#60a5fa"}
          initial={false}
          animate={{
            d: isDark ? sunPath : moonPath,
            fill: isDark ? "#eab308" : "#60a5fa",
            stroke: isDark ? "#eab308" : "#60a5fa",
            fillOpacity: 0.35,
            strokeOpacity: 1,
            rotate: isDark ? 0 : -360,
            scale: isDark ? 1 : 2,
            transition: { duration: 0.3, type: "spring", bounce: 0.3 },
          }}
          style={{ strokeWidth: 6, strokeLinecap: "round" }}
        />
      </motion.svg>
    </button>
  );
};

export default ThemeSwitcher;
