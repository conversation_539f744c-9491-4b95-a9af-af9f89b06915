import { mainNavLinks } from "@/constants";
import React, { useState } from "react";
import MenuItem from "./menu-item";
import { NavDropDownCardItems, NavDropDownListItems } from "@/types";

const NavMenu = () => {
	const [active, setActive] = useState<string | null>(null);

	return (
		<div
			onMouseLeave={() => setActive(null)}
			className="hidden xl:flex flex-row items-center justify-center space-x-4 py-6 px-8 gap-5"
		>
			{mainNavLinks.map((item) => {
				return (
					<MenuItem
						setActive={setActive}
						active={active}
						key={item.label}
						item={item}
						dropDownCardItems={dropDownCardItems}
						dropDownListItems={dropDownListItems}
					/>
				);
			})}
		</div>
	);
};

export const dropDownListItems: NavDropDownListItems = [
	{
		label: "SDG 3: Good Health and Wellbeing",
		route: "/programs/sdg-3"
	},
	{
		label: "SDG 4: Quality Education",
		route: "/programs/sdg-4"
	},
	{
		label: "SDG 5: Gender Equality",
		route: "/programs/sdg-5"
	},
	{
		label: "SDG 13: Climate Control",
		route: "/programs/sdg-13"
	}
];

export const dropDownCardItems: NavDropDownCardItems = [
	{
		title: "Breaking Barriers: Women in Tech",
		id: "/blog",
		imgUrl:
			"https://images.unsplash.com/photo-1************-f5e267bb3db6?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Nnx8d29tZW4lMjBpbiUyMHRlY2h8ZW58MHx8MHx8fDI%3D",
		description:
			"Stories of resilience and success from women breaking into the tech industry."
	},
	{
		title: "Mastering Coding Interviews",
		id: "/blog",
		imgUrl:
			"https://images.unsplash.com/photo-1580894908361-************?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8OHx8d29tZW4lMjBpbiUyMHRlY2h8ZW58MHx8MHx8fDI%3D",
		description:
			"A step-by-step guide to help women ace technical interviews with confidence."
	},
	{
		title: "Your First Open Source Contribution",
		id: "/blog",
		imgUrl:
			"https://images.unsplash.com/photo-1515879218367-8466d910aaa4?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MTZ8fHdvbWVuJTIwaW4lMjB0ZWNofGVufDB8fDB8fHwy",
		description:
			"Get started with open source and make your first contribution with ease."
	},
	{
		title: "Funding & Grants for Women in Tech",
		id: "/blog",
		imgUrl:
			"https://images.unsplash.com/photo-1573167507387-6b4b98cb7c13?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MjB8fHdvbWVuJTIwaW4lMjB0ZWNofGVufDB8fDB8fHwy",
		description:
			"A curated list of scholarships, grants, and funding opportunities for women in tech."
	}
];

export default NavMenu;
