import { mobileNavLinks } from "@/constants";
import { cn } from "@/lib/utils";
import { MenuIcon, X } from "lucide-react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import React, { useEffect, useRef, useState } from "react";
import { motion } from "motion/react";

const MobileMenu = () => {
  const [menuOpen, setMenuOpen] = useState(false);
  const menuRef = useRef<HTMLDivElement | null>(null);
  const pathname = usePathname();

  // Close menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setMenuOpen(false);
      }
    };

    if (menuOpen) {
      document.addEventListener("mousedown", handleClickOutside);
    } else {
      document.removeEventListener("mousedown", handleClickOutside);
    }

    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, [menuOpen]);

  return (
    <div>
      <button onClick={() => setMenuOpen(!menuOpen)} className="xl:hidden">
        {menuOpen ? <X size={20} /> : <MenuIcon size={20} />}
      </button>

      {/* Mobile Dropdown Menu */}
      {menuOpen && (
        <motion.div
          initial={{ opacity: 0, scale: 0.75, y: -20 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          transition={{ duration: 0.3 }}
          ref={menuRef}
          className="absolute xl:hidden py-4 space-y-12 top-16 backdrop-blur-sm overflow-hidden border border-border shadow-xl bg-background/70"
        >
          {mobileNavLinks.map((item) => {
            const isLinkActive =
              pathname === item.route || pathname.startsWith(`${item.route}/`);

            const Icon = item.icon;

            return (
              <Link key={item.route} href={item.route}>
                <div
                  onClick={() => {
                    setMenuOpen(false);
                  }}
                  className={cn(
                    "flex items-center space-x-4 hover:text-muted-foreground px-4 py-2",
                    {
                      "bg-pink-500 hover:text-foreground/60": isLinkActive,
                    }
                  )}
                >
                  <Icon size={20} />
                  <p>{item.label}</p>
                </div>
              </Link>
            );
          })}
        </motion.div>
      )}
    </div>
  );
};

export default MobileMenu;
