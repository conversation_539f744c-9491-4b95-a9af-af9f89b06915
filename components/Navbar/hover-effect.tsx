import { cn } from "@/lib/utils";
import { NavDropDownCardItems } from "@/types";
import { AnimatePresence, motion } from "motion/react";
import { useState } from "react";
import { NavDropDownGridCard } from "../Navbar/nav-drop-down-grid";

export const HoverEffect = ({
  items,
  className,
}: {
  items: NavDropDownCardItems;
  className?: string;
}) => {
  const [hoveredIndex, setHoveredIndex] = useState<number | null>(null);

  return (
    <div className={cn("text-sm grid grid-cols-2 gap-10 p-4", className)}>
      {items.map((item, idx) => (
        <div
          key={item?.description}
          className="relative group  block p-2 h-full w-full"
          onMouseEnter={() => setHoveredIndex(idx)}
          onMouseLeave={() => setHoveredIndex(null)}
        >
          <AnimatePresence>
            {hoveredIndex === idx && (
              <motion.span
                className="absolute inset-0 h-full w-full bg-primary/40 block z-0"
                layoutId="hoverBackground"
                initial={{ opacity: 0 }}
                animate={{
                  opacity: 1,
                  transition: { duration: 1 },
                }}
                exit={{
                  opacity: 0,
                  transition: { duration: 1 },
                }}
              />
            )}
          </AnimatePresence>
          <NavDropDownGridCard item={item} />
        </div>
      ))}
    </div>
  );
};
