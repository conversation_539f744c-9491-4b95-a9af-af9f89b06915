import { NavLink } from "@/constants";
import { NavDropDownCardItems, NavDropDownListItems } from "@/types";
import { usePathname } from "next/navigation";
import React from "react";
import { motion } from "motion/react";
import Link from "next/link";
import { cn } from "@/lib/utils";
import { ChevronDown } from "lucide-react";
import { HoverEffect } from "./hover-effect";

const transition = {
  type: "spring",
  mass: 0.5,
  damping: 11.5,
  stiffness: 100,
  restDelta: 0.001,
  restSpeed: 0.001,
};

const MenuItem = ({
  active,
  setActive,
  item,
  dropDownCardItems,
  dropDownListItems,
}: {
  setActive: (item: string | null) => void;
  active: string | null;
  item: NavLink;
  dropDownCardItems: NavDropDownCardItems | undefined;
  dropDownListItems: NavDropDownListItems | undefined;
}) => {
  const pathname = usePathname();

  const isLinkActive =
    pathname === item.route || pathname.startsWith(`${item.route}/`);

  return (
    <div
      onMouseEnter={() => setActive(item.label)}
      onClick={() => setActive(null)}
      className="relative"
    >
      <motion.div className="cursor-pointer hover:opacity-90">
        <div className="flex flex-row items-center gap-1">
          <Link
            href={item.route}
            className={cn(
              { "text-primary": active === item.label },
              { "text-primary": isLinkActive }
            )}
          >
            {item.label}
          </Link>

          {item.isExpandable && (
            <motion.div
              initial={{ opacity: 1, rotate: 0 }}
              animate={{
                opacity: active === item.label ? 1 : 1,
                rotate: active === item.label ? 180 : 0,
              }}
              transition={{ duration: 0.3 }}
            >
              <ChevronDown
                size={16}
                className={cn(
                  { "text-primary": active === item.label },
                  { "text-primary": isLinkActive }
                )}
              />
            </motion.div>
          )}
        </div>
      </motion.div>

      {active !== null && item.isExpandable && (
        <motion.div
          initial={{ opacity: 0, scale: 0.75, y: -20 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          transition={{ duration: 0.2 }}
        >
          {active === item.label && !item.isListItem && (
            <div className="absolute top-[calc(100%_+_1.2rem)] left-1/2 transform -translate-x-1/2 pt-4">
              <motion.div
                transition={transition}
                layoutId="active" // layoutId ensures smooth animation
                className="backdrop-blur-md overflow-hidden border border-border shadow-xl bg-background"
              >
                <motion.div
                  layout // layout ensures smooth animation
                  className="w-max h-full"
                >
                  <HoverEffect items={dropDownCardItems!} />
                </motion.div>
              </motion.div>
            </div>
          )}

          {active === item.label && item.isListItem && (
            <div className="absolute top-[calc(100%_+_1.2rem)] left-1/2 transform -translate-x-1/2 pt-4">
              <motion.div
                transition={transition}
                layoutId="active" // layoutId ensures smooth animation
                className="backdrop-blur-md overflow-hidden border border-border shadow-xl bg-background"
              >
                <motion.div
                  layout // layout ensures smooth animation
                  className="w-max h-full p-4"
                >
                  <div className="flex flex-col space-y-4 text-sm">
                    {dropDownListItems!.map((item) => {
                      return (
                        <Link
                          key={item.route}
                          href={item.route}
                          className="hover:text-primary/80"
                        >
                          {item.label}
                        </Link>
                      );
                    })}
                  </div>
                </motion.div>
              </motion.div>
            </div>
          )}
        </motion.div>
      )}
    </div>
  );
};

export default MenuItem;
