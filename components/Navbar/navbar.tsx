"use client";

import React from "react";
import <PERSON> from "next/link";
import MobileMenu from "./mobile-menu";
import ThemeAwareNav<PERSON>ogo from "../Logos/theme-aware-nav-logo";
import { Button } from "../ui/button";
// import ThemeSwitcher from "./theme-switcher";
import NavMenu from "./nav-menu";

const Navbar = () => {
  return (
    <header className="fixed top-0 left-0 w-full flex flex-row justify-between max-h-20 h-auto items-center px-5 md:px-12 text-sm shadow-md py-2 bg-white z-50 backdrop-blur-sm bg-background/60">
      {/* Hamburger Menu - Small & Medium Screens */}
      <MobileMenu />

      {/* Logo */}
      <ThemeAwareNavLogo />

      {/* Links */}
      <NavMenu />

      {/* CTA & ThemeSwitcher */}
      <div className="ml-auto flex flex-row gap-4 items-center">
        <Link href={"/get-involved"} className="text-primary">
          <Button
            variant={"secondary"}
            className="hidden xl:block border-r-2  "
          >
            Get Involved
          </Button>
        </Link>
        {/* <ThemeSwitcher /> */}
      </div>
    </header>
  );
};

export default Navbar;
