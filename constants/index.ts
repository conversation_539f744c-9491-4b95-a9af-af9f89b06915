import {
  BookHeart,
  CalendarHeart,
  LucideProps,
  MessageCircleHeart,
  Rss,
  SquareCode,
  SquareLibrary,
  Users,
} from "lucide-react";

// Navbar

export type NavLink = {
  icon: React.ForwardRefExoticComponent<
    Omit<LucideProps, "ref"> & React.RefAttributes<SVGSVGElement>
  >;
  route: string;
  label: string;
  isExpandable: boolean;
  isListItem: boolean;
};

export const mainNavLinks: NavLink[] = [
  {
    icon: BookHeart,
    route: "/about-us",
    label: "About us",
    isExpandable: false,
    isListItem: false,
  },
  {
    icon: SquareCode,
    route: "/programs",
    label: "Our Programs",
    isExpandable: true,
    isListItem: true,
  },
  {
    icon: MessageCircleHeart,
    route: "/impact",
    label: "Impact",
    isExpandable: false,
    isListItem: false,
  },
  {
    icon: Rss,
    route: "/blogs-and-news",
    label: "Blogs & News",
    isExpandable: false,
    isListItem: false,
  },
  {
    icon: CalendarHeart,
    route: "/events",
    label: "Events",
    isExpandable: false,
    isListItem: false,
  },
  {
    icon: SquareLibrary,
    route: "/resources",
    label: "Resources",
    isExpandable: false,
    isListItem: false,
  },
];

export const mobileNavLinks = [
  ...mainNavLinks,
  {
    icon: Users,
    route: "/contact",
    label: "Get Involved",
  },
];

export type FooterLink = {
  label: string;
  route: string;
};

export type FooterColumnLinks = {
  heading: string;
  links: FooterLink[];
};

export const footerQuickLinks: FooterColumnLinks = {
  heading: "Quick Links",
  links: [
    {
      label: "About Us",
      route: "/about-us",
    },
    {
      label: "Our Programs",
      route: "/programs",
    },
    {
      label: "Get Involved",
      route: "/get-involved",
    },
    {
      label: "Contact Us",
      route: "/contact",
    },
    {
      label: "Donate Now",
      route: "/donate",
    },
  ],
};

// Footer

export const footerResourcesLinks: FooterColumnLinks = {
  heading: "Resources",
  links: [
    {
      label: "Blog Posts",
      route: "/blogs-and-news",
    },
    {
      label: "Success Stories",
      route: "/success-stories",
    },
    {
      label: "Media Kit",
      route: "/media-kit",
    },
    {
      label: "Press Releases",
      route: "/press-releases",
    },
    {
      label: "FAQs",
      route: "/faq",
    },
  ],
};

export const footerStayConnectedLinks: FooterColumnLinks = {
  heading: "Stay Connected",
  links: [
    {
      label: "Social Media",
      route: "/social-media",
    },
    {
      label: "Newsletter",
      route: "/blogs-and-news",
    },
    {
      label: "Volunteer",
      route: "/volunteer",
    },
    {
      label: "Events",
      route: "/events",
    },
    {
      label: "Join Us",
      route: "/join-us",
    },
  ],
};

// Socials
export const socials = [
  {
    label: "Facebook",
    route: "https://www.facebook.com/share/14wV6mEyak/",
    icon: "/vectors/facebook-icon.svg",
  },
  {
    label: "Instagram",
    route:
      "https://www.instagram.com/girlcode_movement?igsh=MXNrM2tsc2hxaWxnNQ==",
    icon: "/vectors/instagram-icon.svg",
  },
  {
    label: "LinkedIn",
    route:
      "https://www.linkedin.com/posts/girl-code-organization_3dprinting-techforgood-stemeducation-activity-7297912989976817665-beng?utm_source=share&utm_medium=member_android&rcm=ACoAAE0EWY0BItnGhWpK2zNSsbzAEgxGLtdygf8",
    icon: "/vectors/linkedin-icon.svg",
  },
  {
    label: "X",
    route: "https://twitter.com/girlcode_movement",
    icon: "/vectors/x-icon.svg",
  },
  {
    label: "WhatsApp",
    route: "https://wa.me/************",
    icon: "/vectors/whatsapp-icon.svg",
  },
  {
    label: "TikTok",
    route:
      "https://www.tiktok.com/@girl.code254?_r=1&_d=ecl3db62ai9702&sec_uid=MS4wLjABAAAAEHXzTRy--ftgffEJGfjBFIdXVhlgavfzskNcQ5RndcOFGbYdELye9ZnmkbnKzGHb&share_author_id=7333989548278121478&sharer_language=en&source=h5_m&u_code=ecf533kh8ah3aj&timestamp=**********&user_id=7333989548278121478&sec_user_id=MS4wLjABAAAAEHXzTRy--ftgffEJGfjBFIdXVhlgavfzskNcQ5RndcOFGbYdELye9ZnmkbnKzGHb&utm_source=copy&utm_campaign=client_share&utm_medium=android&share_iid=7472239496315209478&share_link_id=3ceb326c-c570-4a4e-b6ea-05c8df84ed7b&share_app_id=1233&ugbiz_name=ACCOUNT&social_share_type=5&enable_checksum=1",
    icon: "/vectors/tiktok-icon.svg",
  },
];
