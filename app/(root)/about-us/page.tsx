import { Metadata } from "next";
import HeroSection from "@/components/about/HeroSection";
import OurStorySection from "@/components/about/OurStorySection";
import MissionAndVision from "@/components/about/MissionAndVision";
import ImpactStats from "@/components/about/ImpactStats";
import MeetOurTeam from "@/components/about/MeetOurTeam";
// import OurValue from "@/components/about/OurValue";
import JoinUsCTA from "@/components/about/JoinUsCTA";
import OrganizationInfo from "@/components/about/OrganizationInfo";
import { generateSEOMetadata, generateBreadcrumbSchema } from "@/lib/seo";

export const metadata: Metadata = generateSEOMetadata({
  title: "About Us - Our Mission, Vision & Impact",
  description: "Learn about GirlCode Movement's mission to empower women through technology education. Discover our story, team, and the impact we're making across Africa in bridging the gender gap in STEM fields.",
  keywords: [
    "about GirlCode",
    "our mission",
    "our vision",
    "women empowerment",
    "team",
    "nonprofit story",
    "STEM education impact",
    "gender equality mission",
    "technology for good",
    "social impact organization"
  ],
  canonical: "/about-us",
  ogImage: "/images/about-og.jpg",
});

const breadcrumbSchema = generateBreadcrumbSchema([
  { name: "Home", url: "/" },
  { name: "About Us", url: "/about-us" }
]);

export default function AboutUs() {
  return (
    <div>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(breadcrumbSchema),
        }}
      />
      <HeroSection />
      <OurStorySection />
      <MissionAndVision />
      <ImpactStats />
      <MeetOurTeam />
      <OrganizationInfo />
      {/* <OurValue /> */}
      <JoinUsCTA />
    </div>
  );
}
