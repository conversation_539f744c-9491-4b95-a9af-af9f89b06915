import { Metadata } from "next";
import React from "react";
import HeroSection from "@/components/impact/HeroSection";
import ImpactOverview from "@/components/impact/ImpactOverview";
import SuccessStoriesSection from "@/components/impact/SuccessStoriesSection";
import SDGImpactSection from "@/components/impact/SDGImpactSection";
import FinalCTASection from "@/components/impact/FinalCTASection";
import { generateSEOMetadata, generateBreadcrumbSchema } from "@/lib/seo";

export const metadata: Metadata = generateSEOMetadata({
  title: "Our Impact - Transforming Lives Through Technology & Education",
  description:
    "Discover the measurable impact GirlCode Movement has made empowering 5,000+ women across 25 countries. Explore our contribution to UN Sustainable Development Goals through quality education, gender equality, and health & wellbeing initiatives.",
  keywords: [
    "impact statistics",
    "sustainable development goals",
    "women empowerment impact",
    "education outcomes",
    "gender equality results",
    "success stories",
    "community impact",
    "technology training results",
    "SDG contributions",
    "measurable social impact",
    "women in tech statistics",
    "nonprofit impact report",
  ],
  canonical: "/impact",
});

const breadcrumbSchema = generateBreadcrumbSchema([
  { name: "Home", url: "/" },
  { name: "Impact", url: "/impact" },
]);

export default function impact() {
  return (
    <main className="min-h-screen bg-white">
      <HeroSection />
      <ImpactOverview />
      <SuccessStoriesSection />
      <SDGImpactSection />
      <FinalCTASection />
    </main>
  );
}
