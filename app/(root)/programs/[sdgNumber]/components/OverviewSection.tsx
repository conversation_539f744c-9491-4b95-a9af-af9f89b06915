// app/programs/sdg-[number]/components/OverviewSection.tsx
import Image from "next/image";
import { SDGData } from "@/types/sdg";

interface OverviewSectionProps {
  sdg: SDGData;
}

export default function OverviewSection({ sdg }: OverviewSectionProps) {
  return (
    <div className="py-16 px-5 md:px-10 lg:px-16 bg-white">
      <div className="max-w-7xl mx-auto">
        <div className="flex flex-col lg:flex-row gap-10 lg:gap-16 items-start">
          <div className="lg:w-1/2">
            <div className="inline-flex items-center gap-2 bg-primary/10 py-1 px-3 rounded-full w-fit mb-4">
              <span className="text-sm font-medium text-primary">
                SDG {sdg.number}
              </span>
            </div>
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              Understanding {sdg.title}
            </h2>
            <div className="space-y-4">
              <p className="text-gray-700">{sdg.longDescription}</p>
              <h3 className="text-xl font-bold mt-6 mb-3">Our Approach</h3>
              <p className="text-gray-700">{sdg.approach}</p>
            </div>

            <div className={`${sdg.bgColor} ${sdg.borderColor} border p-6 rounded-lg mt-8`}>
              <div className="flex items-center gap-4 mb-4">
                <div className={`${sdg.iconBgColor} rounded-full p-3`}>
                  <Image
                    src={sdg.icon}
                    alt={sdg.title}
                    width={24}
                    height={24}
                    className="w-6 h-6"
                  />
                </div>
                <h4 className="text-lg font-bold">Key Focus Areas</h4>
              </div>
              <ul className="grid gap-3">
                {sdg.programs.map((program) => (
                  <li key={program.id} className="flex items-start gap-2">
                    <div className={`w-2 h-2 rounded-full ${sdg.buttonColor} mt-2`}></div>
                    <span>{program.name}</span>
                  </li>
                ))}
              </ul>
            </div>
          </div>

          <div className="lg:w-1/2">
            <div className="rounded-xl overflow-hidden shadow-lg">
              <div className="relative w-full pt-[60%]">
                <Image
                  src={sdg.detailImage}
                  alt={`${sdg.title} initiatives`}
                  fill
                  className="object-cover"
                />
              </div>
              <div className={`${sdg.bgColor} p-6`}>
                <h3 className={`text-xl font-bold ${sdg.textColor} mb-2`}>Why SDG {sdg.number} Matters</h3>
                <p className="text-gray-700">
                  {sdg.number === 3 && "Good health and well-being are fundamental to living a fulfilling life. By addressing mental health needs, particularly for young people, we build the foundation for resilient, thriving communities."}
                  {sdg.number === 4 && "Quality education is the cornerstone of development and empowerment. By providing inclusive, practical education in technology and financial literacy, we create pathways to economic independence."}
                  {sdg.number === 5 && "Gender equality is not just a human right, but a necessary foundation for a peaceful, prosperous world. By addressing discrimination and violence against women and girls, we unlock their potential as agents of change."}
                  {sdg.number === 13 && "Climate action is urgent and necessary to protect our planet for future generations. By teaching sustainable practices and green innovation, we contribute to environmental solutions while creating economic opportunities."}
                </p>
              </div>
            </div>

            <div className="mt-8 flex items-center p-4 border border-gray-200 rounded-lg bg-gray-50">
              <div className="mr-4">
                <div className="bg-primary/10 p-2 rounded-full">
                  <svg 
                    xmlns="http://www.w3.org/2000/svg" 
                    width="24" 
                    height="24" 
                    viewBox="0 0 24 24" 
                    fill="none" 
                    stroke="currentColor" 
                    strokeWidth="2" 
                    strokeLinecap="round" 
                    strokeLinejoin="round" 
                    className="text-primary"
                  >
                    <circle cx="12" cy="12" r="10"></circle>
                    <line x1="12" y1="8" x2="12" y2="16"></line>
                    <line x1="8" y1="12" x2="16" y2="12"></line>
                  </svg>
                </div>
              </div>
              <div>
                <p className="font-medium mb-1">Want to learn more about SDG {sdg.number}?</p>
                <p className="text-sm text-gray-600">
                  Check out the UN Sustainable Development Goals website for global initiatives and progress on {sdg.title}.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}