// app/programs/sdg-[number]/components/ResourcesSection.tsx
import Link from "next/link";
import { Button } from "@/components/ui/button";
import UniversalCard from "@/components/ui/universal-card";
import { SDGData } from "@/types/sdg";

interface ResourcesSectionProps {
  sdg: SDGData;
}

export default function ResourcesSection({ sdg }: ResourcesSectionProps) {
  return (
    <div className="py-16 px-5 md:px-10 lg:px-16 bg-white">
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-12">
          <div
            className={`${sdg.iconBgColor} w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-4`}
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className={`${sdg.textColor}`}
            >
              <path d="M4 19.5A2.5 2.5 0 0 1 6.5 17H20"></path>
              <path d="M6.5 2H20v20H6.5A2.5 2.5 0 0 1 4 19.5v-15A2.5 2.5 0 0 1 6.5 2z"></path>
            </svg>
          </div>
          <h2 className="font-bold text-3xl md:text-4xl mb-4">
            {sdg.title} Resources
          </h2>
          <p className="text-base md:text-lg text-gray-600 max-w-2xl mx-auto">
            Access learning materials, guides, and support documentation related
            to our
            {sdg.title.toLowerCase()} programs and initiatives.
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
          {sdg.resources.map((resource) => (
            <UniversalCard
              key={resource.id}
              type="resource"
              data={{
                id: resource.id,
                title: resource.title,
                description: resource.description,
                image: resource.image,
                category: resource.type,
                link: resource.link,
                color: resource.color,
                bgColor: resource.iconBgColor,
                ctaText:
                  resource.type === "PDF" || resource.type === "Downloads"
                    ? "Download"
                    : resource.type === "Contacts"
                    ? "View Directory"
                    : "Access Resource",
              }}
              layout="grid"
              showImage={false}
              showMeta={true}
            />
          ))}
        </div>

        <div className="flex justify-center mt-12">
          <Button
            variant="outline"
            className={`border-${sdg.color.replace("bg-", "")} ${
              sdg.textColor
            } hover:bg-${sdg.color.replace("bg-", "")}/5`}
          >
            <Link href="/resources">View All Resources</Link>
          </Button>
        </div>
      </div>
    </div>
  );
}
