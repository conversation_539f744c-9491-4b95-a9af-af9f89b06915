// app/programs/sdg-[number]/components/FAQSection.tsx
import { useState } from "react";
import { ChevronDown, ChevronUp } from "lucide-react";
import { SDGData, FAQ } from "@/types/sdg";

interface FAQSectionProps {
  sdg: SDGData;
}

function FAQItem({ faq, index, isOpen, toggle }: { 
  faq: FAQ; 
  index: number; 
  isOpen: boolean;
  toggle: () => void;
}) {
  return (
    <div className="border border-gray-200 rounded-lg overflow-hidden">
      <button 
        className="w-full flex justify-between items-center p-4 text-left font-medium hover:bg-gray-50 transition-colors"
        onClick={toggle}
        aria-expanded={isOpen}
        aria-controls={`faq-answer-${index}`}
      >
        <span>{faq.question}</span>
        {isOpen ? (
          <ChevronUp className="w-5 h-5 text-gray-500" />
        ) : (
          <ChevronDown className="w-5 h-5 text-gray-500" />
        )}
      </button>
      <div 
        id={`faq-answer-${index}`}
        className={`px-4 overflow-hidden transition-all duration-300 ${
          isOpen ? "pb-4 max-h-96" : "max-h-0"
        }`}
      >
        <p className="text-gray-600">{faq.answer}</p>
      </div>
    </div>
  );
}

export default function FAQSection({ sdg }: FAQSectionProps) {
  const [openIndex, setOpenIndex] = useState(0);

  if (sdg.faqs.length === 0) {
    return null;
  }

  return (
    <div className="py-16 px-5 md:px-10 lg:px-16 bg-white border-t border-gray-100">
      <div className="max-w-4xl mx-auto">
        <div className="text-center mb-12">
          <div className="inline-flex items-center gap-2 bg-primary/10 py-1 px-3 rounded-full w-fit mx-auto mb-4">
            <span className="text-sm font-medium text-primary">
              Common Questions
            </span>
          </div>
          <h2 className="font-bold text-3xl md:text-4xl mb-4">
            Frequently Asked Questions
          </h2>
          <p className="text-base md:text-lg text-gray-600 max-w-2xl mx-auto">
            Find answers to common questions about our {sdg.title.toLowerCase()} programs, 
            application process, and how you can get involved.
          </p>
        </div>

        <div className="space-y-4">
          {sdg.faqs.map((faq, index) => (
            <FAQItem 
              key={index} 
              faq={faq} 
              index={index}
              isOpen={index === openIndex}
              toggle={() => setOpenIndex(index === openIndex ? -1 : index)}
            />
          ))}
        </div>

        <div className="mt-12 text-center p-6 bg-gray-50 rounded-lg">
          <h3 className="font-bold text-lg mb-2">Still have questions?</h3>
          <p className="text-gray-600 mb-4">
            We{"'"}re here to help! Reach out to our team for more information about our {sdg.title.toLowerCase()} initiatives.
          </p>
          <a 
            href="/contact" 
            className="inline-block bg-primary text-white px-4 py-2 rounded hover:bg-primary/90 transition-colors"
          >
            Contact Us
          </a>
        </div>
      </div>
    </div>
  );
}