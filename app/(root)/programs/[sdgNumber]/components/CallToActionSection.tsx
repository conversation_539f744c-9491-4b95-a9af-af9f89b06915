// app/programs/sdg-[number]/components/CallToActionSection.tsx
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { SDGData } from "@/types/sdg";

interface CallToActionSectionProps {
  sdg: SDGData;
}

export default function CallToActionSection({ sdg }: CallToActionSectionProps) {
  return (
    <div className="w-full py-20 px-5 md:px-10 lg:px-16 bg-gray-50">
      <div className="max-w-7xl mx-auto">
        <div className={`relative rounded-2xl overflow-hidden bg-${sdg.color.replace('bg-', '')} shadow-lg`}>
          {/* Background gradient */}
          <div className="absolute inset-0 bg-gradient-to-r from-black/30 via-transparent to-transparent -z-10"></div>

          {/* Decorative elements */}
          <div className="absolute top-0 right-0 w-64 h-64 bg-white/5 rounded-full -translate-y-1/2 translate-x-1/2 -z-10"></div>
          <div className="absolute bottom-0 left-0 w-48 h-48 bg-white/10 rounded-full translate-y-1/3 -translate-x-1/4 -z-10"></div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-0">
            {/* Content side */}
            <div className="p-8 md:p-12 lg:p-16 flex flex-col justify-center">
              <div className="inline-flex items-center gap-2 bg-white/10 py-1 px-3 rounded-full w-fit mb-6">
                <span className="text-sm font-medium text-white">Join Our Mission</span>
              </div>

              <h2 className="font-bold text-3xl md:text-4xl lg:text-5xl mb-6 leading-tight text-white">
                Be Part of Our {sdg.title} Initiatives
              </h2>

              <p className="text-base md:text-lg text-white/90 mb-6 max-w-lg">
                {sdg.number === 3 && "Your support can help us create more resources, expand our peer support networks, and bring mental health education to more communities across Kenya."}
                {sdg.number === 4 && "Your involvement can help us provide quality education and skills training to more women and girls, opening doors to economic opportunity and independence."}
                {sdg.number === 5 && "Your participation can strengthen our efforts to end gender-based violence, support survivors, and create safe spaces for women and girls to thrive."}
                {sdg.number === 13 && "Your contribution can advance our sustainable manufacturing initiatives, expand eco-friendly 3D printing programs, and develop more climate innovation projects."}
              </p>

              <div className="flex flex-col sm:flex-row gap-4 mb-6">
                <Button className="bg-white hover:bg-white/90 text-black font-medium rounded-lg">
                  <Link href="/get-involved" className="flex items-center gap-2">
                    Get Involved
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="16"
                      height="16"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    >
                      <path d="M5 12h14M12 5l7 7-7 7" />
                    </svg>
                  </Link>
                </Button>
                <Button
                  variant="outline"
                  className="border-white text-white hover:bg-white/10"
                >
                  <Link href="/donate">Support Our Work</Link>
                </Button>
              </div>

              <div className="mt-2 border-l-4 border-white/30 pl-4 py-1 italic text-white/80 text-sm">
                {sdg.number === 3 && "Every contribution helps us support the mental health and wellbeing of more young people across Kenya."}
                {sdg.number === 4 && "Your support helps break down barriers to quality education for women and girls in underserved communities."}
                {sdg.number === 5 && "Together, we can create a more equal world where all women and girls can reach their full potential."}
                {sdg.number === 13 && "Join us in taking meaningful climate action through education, innovation, and sustainable practices."}
              </div>
            </div>

            {/* Action Card side */}
            <div className="p-8 md:p-12 lg:p-16 bg-white/10 backdrop-blur-sm">
              <div className="bg-white rounded-xl p-8 shadow-lg">
                <h3 className="text-2xl font-bold mb-6">Ways to Support SDG {sdg.number}</h3>
                
                <div className="space-y-4 mb-8">
                  <div className="flex items-start gap-3">
                    <div className="flex-shrink-0 mt-1">
                      <div className={`w-6 h-6 rounded-full ${sdg.buttonColor} flex items-center justify-center text-white text-xs font-bold`}>1</div>
                    </div>
                    <div>
                      <h4 className="font-medium">Volunteer Your Skills</h4>
                      <p className="text-sm text-gray-600">
                        Share your expertise as a mentor, trainer, or program assistant.
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-start gap-3">
                    <div className="flex-shrink-0 mt-1">
                      <div className={`w-6 h-6 rounded-full ${sdg.buttonColor} flex items-center justify-center text-white text-xs font-bold`}>2</div>
                    </div>
                    <div>
                      <h4 className="font-medium">Become a Partner</h4>
                      <p className="text-sm text-gray-600">
                        Organizations can support our programs through funding, resources, or expertise.
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-start gap-3">
                    <div className="flex-shrink-0 mt-1">
                      <div className={`w-6 h-6 rounded-full ${sdg.buttonColor} flex items-center justify-center text-white text-xs font-bold`}>3</div>
                    </div>
                    <div>
                      <h4 className="font-medium">Donate to Our Programs</h4>
                      <p className="text-sm text-gray-600">
                        Your financial contribution helps us expand our impact and reach more communities.
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-start gap-3">
                    <div className="flex-shrink-0 mt-1">
                      <div className={`w-6 h-6 rounded-full ${sdg.buttonColor} flex items-center justify-center text-white text-xs font-bold`}>4</div>
                    </div>
                    <div>
                      <h4 className="font-medium">Spread the Word</h4>
                      <p className="text-sm text-gray-600">
                        Follow us on social media and help raise awareness about our initiatives.
                      </p>
                    </div>
                  </div>
                </div>
                
                <Button className={`${sdg.buttonColor} text-white w-full`}>
                  <Link href="/contact">Contact Us Today</Link>
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}