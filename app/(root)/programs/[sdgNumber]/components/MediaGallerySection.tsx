// app/programs/sdg-[number]/components/MediaGallerySection.tsx
import Image from "next/image";
import Link from "next/link";
import { ChevronRight } from "lucide-react";
import { Button } from "@/components/ui/button";
import { SDGData } from "@/types/sdg";

// Placeholder media gallery data
// In a real application, this would come from a CMS or API
const getGalleryItems = (sdgNumber: number) => {
	const commonImages = [
		"/images/empowering-image.JPG",
		"/images/inspiring-change.jpg",
		"/images/certificate-image.jpg",
		"/images/education-image.png"
	];

	// Add specific images based on SDG
	if (sdgNumber === 3) {
		return [
			{
				type: "image",
				src: "/images/inspiring-change.jpg",
				alt: "Mental health workshop",
				caption: "Teen mental health workshop in Nairobi"
			},
			{
				type: "image",
				src: "/images/certificate-image.jpg",
				alt: "Self-help handbook distribution",
				caption: "Distributing self-help handbooks at a local school"
			},
			{
				type: "image",
				src: "/images/empowering-image.JPG",
				alt: "Peer support training",
				caption: "Peer support network facilitator training"
			},
			{
				type: "image",
				src: "/images/inspiring-change.jpg",
				alt: "Mental health seminar",
				caption: "Community seminar on digital wellbeing"
			},
			{
				type: "video",
				src: "/images/certificate-image.jpg",
				thumbnail: "/images/certificate-image.jpg",
				alt: "Mental health testimonial",
				caption: "Impact stories from our mental health program"
			},
			{
				type: "image",
				src: "/images/empowering-image.JPG",
				alt: "School outreach",
				caption: "School outreach program in Mombasa"
			}
		];
	} else if (sdgNumber === 4) {
		return [
			{
				type: "image",
				src: "/images/education-image.png",
				alt: "Coding class",
				caption: "Coding class at GirlCode Hub"
			},
			{
				type: "image",
				src: "/images/certificate-image.jpg",
				alt: "Graduation ceremony",
				caption: "Bootcamp graduation ceremony"
			},
			{
				type: "image",
				src: "/images/empowering-image.JPG",
				alt: "Financial workshop",
				caption: "Financial literacy workshop"
			},
			{
				type: "video",
				src: "/images/certificate-image.jpg",
				thumbnail: "/images/education-image.png",
				alt: "Student testimonial",
				caption: "Success stories from our education programs"
			},
			{
				type: "image",
				src: "/images/certificate-image.jpg",
				alt: "Computer training",
				caption: "Computer skills training in Kisumu"
			},
			{
				type: "image",
				src: "/images/education-image.png",
				alt: "Classroom session",
				caption: "Web development classroom session"
			}
		];
	} else if (sdgNumber === 5) {
		return [
			{
				type: "image",
				src: "/images/empowering-image.JPG",
				alt: "Women's empowerment",
				caption: "Safe space workshop for women"
			},
			{
				type: "image",
				src: "/images/inspiring-change.jpg",
				alt: "GBV awareness",
				caption: "GBV awareness campaign in Nairobi"
			},
			{
				type: "video",
				src: "/images/certificate-image.jpg",
				thumbnail: "/images/empowering-image.JPG",
				alt: "Rescue center interview",
				caption: "Interview with rescue center director"
			},
			{
				type: "image",
				src: "/images/certificate-image.jpg",
				alt: "Digital skills",
				caption: "Digital skills training for survivors"
			},
			{
				type: "image",
				src: "/images/inspiring-change.jpg",
				alt: "Advocacy event",
				caption: "Gender equality advocacy event"
			},
			{
				type: "image",
				src: "/images/empowering-image.JPG",
				alt: "Community support",
				caption: "Community support network meeting"
			}
		];
	} else if (sdgNumber === 13) {
		return [
			{
				type: "image",
				src: "/images/image-3.jpg",
				alt: "3D printing",
				caption: "Eco-friendly 3D printing workshop"
			},
			{
				type: "image",
				src: "/images/empowering-image.JPG",
				alt: "Sustainability training",
				caption: "Sustainable manufacturing training"
			},
			{
				type: "image",
				src: "/images/certificate-image.jpg",
				alt: "Recycled materials",
				caption: "Processing recycled materials for production"
			},
			{
				type: "video",
				src: "/images/certificate-image.jpg",
				thumbnail: "/images/image-3.jpg",
				alt: "Climate innovation",
				caption: "Climate innovation hackathon highlights"
			},
			{
				type: "image",
				src: "/images/education-image.png",
				alt: "Green design",
				caption: "Green product design workshop"
			},
			{
				type: "image",
				src: "/images/image-3.jpg",
				alt: "Prototype testing",
				caption: "Testing sustainable product prototypes"
			}
		];
	}

	// Default gallery
	return commonImages.map((src, index) => ({
		type: "image" as const,
		src,
		alt: `Gallery image ${index + 1}`,
		caption: `Program activity ${index + 1}`
	}));
};

interface MediaGallerySectionProps {
	sdg: SDGData;
}

export default function MediaGallerySection({ sdg }: MediaGallerySectionProps) {
	const galleryItems = getGalleryItems(sdg.number);

	return (
		<div className={`${sdg.bgColor} py-16 px-5 md:px-10 lg:px-16`}>
			<div className="max-w-7xl mx-auto">
				<div className="flex flex-col gap-2 mb-12 text-center">
					<div className="inline-flex mx-auto items-center gap-2 bg-white/50 py-1 px-3 rounded-full w-fit mb-4">
						<span className={`text-sm font-medium ${sdg.textColor}`}>
							Media Gallery
						</span>
					</div>
					<h2 className="font-bold text-3xl md:text-4xl mb-4">
						See Our {sdg.title} Programs in Action
					</h2>
					<p className="text-base md:text-lg text-gray-700 max-w-3xl mx-auto">
						Images and videos from our {sdg.title.toLowerCase()} initiatives and
						activities.
					</p>
				</div>

				<div className="grid grid-cols-2 md:grid-cols-3 gap-4">
					{galleryItems.map((item, index) => (
						<div
							key={index}
							className="relative group overflow-hidden rounded-lg shadow-md"
						>
							<div className="relative aspect-[4/3]">
								<Image
									src={
										// item.type === "video" ? item.thumbnail :
										item.src
									}
									alt={item.alt}
									fill
									className="object-cover transition-transform duration-500 group-hover:scale-105"
								/>
								<div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity"></div>

								{item.type === "video" && (
									<div className="absolute inset-0 flex items-center justify-center">
										<div className="bg-white/20 backdrop-blur-sm rounded-full p-3">
											<svg
												xmlns="http://www.w3.org/2000/svg"
												width="24"
												height="24"
												viewBox="0 0 24 24"
												fill="white"
												stroke="none"
											>
												<polygon points="5 3 19 12 5 21 5 3"></polygon>
											</svg>
										</div>
									</div>
								)}

								<div className="absolute bottom-0 left-0 right-0 p-4 text-white translate-y-full group-hover:translate-y-0 transition-transform">
									<p className="text-sm">{item.caption}</p>
								</div>
							</div>
						</div>
					))}
				</div>

				<div className="mt-12 text-center">
					<Button
						variant="outline"
						className={`border-white ${sdg.textColor} hover:bg-white/20`}
					>
						<Link
							href={`/gallery/sdg-${sdg.number}`}
							className="flex items-center gap-2"
						>
							View Full Gallery
							<ChevronRight className="h-4 w-4" />
						</Link>
					</Button>
				</div>
			</div>
		</div>
	);
}
