// app/programs/sdg-[number]/components/UpcomingEventsSection.tsx
import Image from "next/image";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { SDGData } from "@/types/sdg";
import eventsData, { Event } from "@/data/eventsData";

interface UpcomingEventsSectionProps {
  sdg: SDGData;
}

function EventCard({ event }: { event: Event }) {
  return (
    <div className="bg-white rounded-lg shadow-md overflow-hidden flex flex-col h-full hover:shadow-lg transition-all">
      <div className="relative h-48">
        <Image
          src={event.image}
          alt={event.title}
          fill
          className="object-cover"
          onError={(e) => {
            e.currentTarget.src = "/images/placeholder-event.jpg";
          }}
        />
        <div className="absolute top-0 right-0 bg-primary text-white px-3 py-1 m-2 rounded text-sm">
          {event.category}
        </div>
      </div>
      <div className="p-6 flex-grow flex flex-col justify-between">
        <div>
          <h3 className="text-xl font-bold mb-2 text-gray-900">
            {event.title}
          </h3>
          <div className="flex items-center mb-2 text-gray-600">
            <svg
              className="w-4 h-4 mr-1"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
              />
            </svg>
            <span className="text-sm">{event.date}</span>
          </div>
          <div className="flex items-center mb-2 text-gray-600">
            <svg
              className="w-4 h-4 mr-1"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
            <span className="text-sm">{event.time}</span>
          </div>
          <div className="flex items-center mb-4 text-gray-600">
            <svg
              className="w-4 h-4 mr-1"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
              />
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
              />
            </svg>
            <span className="text-sm">{event.location}</span>
          </div>
          <p className="text-gray-600 mb-4 text-sm">{event.description}</p>

          {/* Registration info */}
          <div className="bg-gray-50 p-3 rounded-lg mb-4">
            <div className="flex justify-between mb-1">
              <span className="text-sm font-medium">Registration:</span>
              <span className="text-sm">{event.registration?.fee}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm font-medium">Deadline:</span>
              <span className="text-sm">{event.registration?.deadline}</span>
            </div>
          </div>
        </div>

        {/* Register button */}
        <Link
          href={event.registration?.url ?? "#"}
          className="inline-block px-4 py-2 rounded text-sm text-center bg-primary text-white hover:bg-opacity-90 transition-colors"
        >
          View Event
        </Link>
      </div>
    </div>
  );
}

export default function UpcomingEventsSection({
  sdg,
}: UpcomingEventsSectionProps) {
  // Filter upcoming events related to this SDG
  const relatedEvents = eventsData
    .filter(
      (event) =>
        event.relatedSDGs.includes(sdg.number) && event.status === "upcoming"
    )
    .slice(0, 3); // Limit to 3 events

  if (relatedEvents.length === 0) {
    return null; // Don't render this section if no upcoming events
  }

  return (
    <div className="py-16 px-5 md:px-10 lg:px-16 bg-white border-t border-gray-100">
      <div className="max-w-7xl mx-auto">
        <div className="flex flex-col gap-2 mb-12 text-center">
          <div className="inline-flex mx-auto items-center gap-2 bg-primary/10 py-1 px-3 rounded-full w-fit mb-4">
            <span className="text-sm font-medium text-primary">
              Upcoming Events
            </span>
          </div>
          <h2 className="font-bold text-3xl md:text-4xl mb-4">
            Join Our {sdg.title} Events
          </h2>
          <p className="text-base md:text-lg text-gray-700 max-w-3xl mx-auto">
            Participate in our upcoming workshops, webinars, and training
            sessions focused on {sdg.title.toLowerCase()}.
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
          {relatedEvents.map((event) => (
            <EventCard key={event.id} event={event} />
          ))}
        </div>

        <div className="mt-12 text-center">
          <Button
            variant="outline"
            className="border-primary text-primary hover:bg-primary/5"
          >
            <Link href="/events" className="flex items-center gap-2">
              View All Events
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <path d="M9 18l6-6-6-6" />
              </svg>
            </Link>
          </Button>
        </div>
      </div>
    </div>
  );
}
