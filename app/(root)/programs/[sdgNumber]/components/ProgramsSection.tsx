// app/programs/sdg-[number]/components/ProgramsSection.tsx
import Link from "next/link";
import { ChevronRight } from "lucide-react";
import { <PERSON>ton } from "@/components/ui/button";
import UniversalCard from "@/components/ui/universal-card";
import { SDGData } from "@/types/sdg";

interface ProgramsSectionProps {
  sdg: SDGData;
}

export default function ProgramsSection({ sdg }: ProgramsSectionProps) {
  return (
    <div className={`${sdg.bgColor} py-16 px-5 md:px-10 lg:px-16`}>
      <div className="max-w-7xl mx-auto">
        <div className="flex flex-col gap-2 mb-12 text-center">
          <div className="inline-flex mx-auto items-center gap-2 bg-white/50 py-1 px-3 rounded-full w-fit mb-4">
            <span className={`text-sm font-medium ${sdg.textColor}`}>
              Our Programs
            </span>
          </div>
          <h2 className="font-bold text-3xl md:text-4xl mb-4">
            {sdg.shortTitle} Initiatives
          </h2>
          <p className="text-base md:text-lg text-gray-700 max-w-3xl mx-auto">
            Our {sdg.title.toLowerCase()} programs create meaningful impact
            through innovative, sustainable approaches that empower communities.
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
          {sdg.programs.map((program) => (
            <UniversalCard
              key={program.id}
              type="program"
              data={{
                id: program.id,
                title: program.name,
                description: program.overview,
                image: program.image,
                link: program.link,
                highlights:
                  program.curriculum ||
                  program.support ||
                  program.impact ||
                  program.benefits ||
                  program.details ||
                  program.outcomes ||
                  [],
                color: sdg.buttonColor,
                textColor: sdg.textColor,
              }}
              layout="grid"
              showImage={false}
              showMeta={false}
            />
          ))}
        </div>

        <div className="mt-16 text-center">
          <p className="font-medium mb-4">
            All our programs align with the UN Sustainable Development Goals
          </p>
          <Button className={`${sdg.buttonColor}`}>
            <Link href="/programs" className="flex items-center gap-2">
              View All Programs
              <ChevronRight className="h-4 w-4" />
            </Link>
          </Button>
        </div>
      </div>
    </div>
  );
}
