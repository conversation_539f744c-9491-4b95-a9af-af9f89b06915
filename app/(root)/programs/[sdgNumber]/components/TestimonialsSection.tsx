// app/programs/sdg-[number]/components/TestimonialsSection.tsx
import UniversalCard from "@/components/ui/universal-card";
import { SDGData } from "@/types/sdg";

interface TestimonialsSectionProps {
  sdg: SDGData;
}

export default function TestimonialsSection({ sdg }: TestimonialsSectionProps) {
  return (
    <div className="py-16 px-5 md:px-10 lg:px-16 bg-gray-50">
      <div className="max-w-7xl mx-auto">
        <div className="flex flex-col gap-2 mb-12 text-center">
          <div className="inline-flex mx-auto items-center gap-2 bg-primary/10 py-1 px-3 rounded-full w-fit mb-4">
            <span className="text-sm font-medium text-primary">
              Voices of Change
            </span>
          </div>
          <h2 className="font-bold text-3xl md:text-4xl mb-4">Testimonials</h2>
          <p className="text-base md:text-lg text-gray-700 max-w-3xl mx-auto">
            Hear directly from our program participants, partners, and community
            members about the impact of our {sdg.title.toLowerCase()}{" "}
            initiatives.
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {sdg.testimonials.map((testimonial) => (
            <UniversalCard
              key={testimonial.id}
              type="testimonial"
              data={{
                id: testimonial.id,
                title: testimonial.author,
                description: testimonial.quote,
                image: testimonial.image,
                author: testimonial.author,
                authorImage: testimonial.image,
                role: testimonial.role,
              }}
              layout="grid"
              showImage={false}
              showMeta={false}
            />
          ))}
        </div>
      </div>
    </div>
  );
}
