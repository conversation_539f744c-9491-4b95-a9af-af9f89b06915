// app/programs/sdg-[number]/components/RelatedBlogsSection.tsx
import Image from "next/image";
import Link from "next/link";
import { ChevronRight } from "lucide-react";
import { Button } from "@/components/ui/button";
import { SDGData } from "@/types/sdg";
import blogPosts, { BlogPost } from "@/data/blogData";

interface RelatedBlogsSectionProps {
  sdg: SDGData;
}

function BlogCard({ post }: { post: BlogPost }) {
  return (
    <div className="bg-white rounded-xl shadow-md overflow-hidden flex flex-col h-full hover:shadow-lg transition-all border border-gray-100 group">
      <div className="relative h-52">
        <Image
          src={post.coverImage}
          alt={post.title}
          fill
          className="object-cover group-hover:scale-105 transition-transform duration-500"
          onError={(e) => {
            e.currentTarget.src = "/images/certificate-image.jpg";
          }}
        />
        <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent"></div>
        <div className="absolute top-4 left-4">
          <div
            className={`${post.color} px-3 py-1 rounded-full text-white text-xs font-medium`}
          >
            {post.category}
          </div>
        </div>
        <div className="absolute bottom-4 right-4">
          <span className="bg-black/30 backdrop-blur-sm text-white text-xs px-3 py-1 rounded-full">
            {post.readTime}
          </span>
        </div>
      </div>
      <div className="p-6 flex-grow flex flex-col justify-between">
        <div>
          <h3 className="text-xl font-bold mb-3 text-gray-900 group-hover:text-primary transition-colors">
            {post.title}
          </h3>
          <p className="text-gray-600 mb-4 text-sm">{post.excerpt}</p>
          <div className="flex flex-wrap gap-2 mb-4">
            {post.tags.slice(0, 2).map((tag) => (
              <span
                key={tag}
                className={`${post.lightColor} text-gray-700 text-xs px-2 py-1 rounded-full`}
              >
                {tag}
              </span>
            ))}
            {post.tags.length > 2 && (
              <span className="bg-gray-100 text-gray-600 text-xs px-2 py-1 rounded-full">
                +{post.tags.length - 2}
              </span>
            )}
          </div>
        </div>
        <div>
          <div className="flex items-center justify-between mb-4 pt-4 border-t border-gray-100">
            <div className="flex items-center">
              <div className="relative w-8 h-8 rounded-full overflow-hidden border-2 border-white mr-2">
                <Image
                  src={post.authorImage}
                  alt={post.author}
                  fill
                  className="object-cover"
                  onError={(e) => {
                    e.currentTarget.src = "/images/certificate-image.jpg";
                  }}
                />
              </div>
              <span className="text-xs font-medium text-gray-900">
                {post.author}
              </span>
            </div>
            <div className="text-xs text-gray-500">{post.date}</div>
          </div>
          <Button
            variant="ghost"
            className="w-full justify-center text-primary hover:bg-primary/5"
          >
            <Link href={`/blog/${post.id}`} className="flex items-center gap-1">
              <span>Read Article</span>
              <ChevronRight className="h-4 w-4" />
            </Link>
          </Button>
        </div>
      </div>
    </div>
  );
}

export default function RelatedBlogsSection({ sdg }: RelatedBlogsSectionProps) {
  // Filter blog posts related to this SDG
  const relatedPosts = blogPosts.filter(post => 
    post.relatedSDGs.includes(sdg.number)
  ).slice(0, 3); // Limit to 3 posts

  if (relatedPosts.length === 0) {
    return null; // Don't render this section if no related blog posts
  }

  return (
    <div className={`${sdg.bgColor} py-16 px-5 md:px-10 lg:px-16`}>
      <div className="max-w-7xl mx-auto">
        <div className="flex flex-col gap-2 mb-12 text-center">
          <div className="inline-flex mx-auto items-center gap-2 bg-white/50 py-1 px-3 rounded-full w-fit mb-4">
            <span className={`text-sm font-medium ${sdg.textColor}`}>
              From Our Blog
            </span>
          </div>
          <h2 className="font-bold text-3xl md:text-4xl mb-4">
            Articles About {sdg.title}
          </h2>
          <p className="text-base md:text-lg text-gray-700 max-w-3xl mx-auto">
            Explore insights, stories, and updates related to our {sdg.title.toLowerCase()} initiatives.
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
          {relatedPosts.map((post) => (
            <BlogCard key={post.id} post={post} />
          ))}
        </div>

        <div className="mt-12 text-center">
          <Button 
            variant="outline"
            className={`border-${sdg.color.replace('bg-', '')} ${sdg.textColor} hover:bg-${sdg.color.replace('bg-', '')}/5`}
          >
            <Link href="/blog" className="flex items-center gap-2">
              View All Blog Posts
              <ChevronRight className="h-4 w-4" />
            </Link>
          </Button>
        </div>
      </div>
    </div>
  );
}