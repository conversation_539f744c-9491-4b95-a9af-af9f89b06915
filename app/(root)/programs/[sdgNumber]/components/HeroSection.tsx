// app/programs/sdg-[number]/components/HeroSection.tsx
import Image from "next/image";
import { SDGData } from "@/types/sdg";

interface HeroSectionProps {
  sdg: SDGData;
}

export default function HeroSection({ sdg }: HeroSectionProps) {
  return (
    <div className="relative w-full h-[50vh] overflow-hidden">
      {/* Background image */}
      <div className="absolute inset-0">
        <Image
          src={sdg.heroImage}
          alt={`SDG ${sdg.number}: ${sdg.title}`}
          fill
          className="object-cover"
          priority
        />
        <div className="absolute inset-0 bg-gradient-to-r from-black/70 via-black/40 to-transparent"></div>
      </div>

      {/* Content overlay */}
      <div className="relative h-full z-10">
        <div className="max-w-7xl mx-auto px-5 md:px-10 lg:px-16 h-full flex items-center">
          <div className="max-w-2xl">
            <div className="w-16 h-1 bg-primary mb-8"></div>

            <div className="flex items-center gap-3 mb-4">
              <div
                className={`${sdg.iconBgColor} rounded-full w-12 h-12 flex items-center justify-center`}
              >
                <Image
                  src={sdg.icon}
                  alt={sdg.title}
                  height={28}
                  width={28}
                  className="w-7 h-7"
                />
              </div>
              <div
                className={`${sdg.buttonColor}  p-1 text-white text-xs font-semibold`}
              >
                SDG {sdg.number}
              </div>
            </div>

            <h1 className="font-bold text-4xl md:text-5xl text-white mb-6 leading-tight">
              {sdg.title}
            </h1>

            <p className="text-base md:text-lg text-gray-200 mb-10 max-w-md">
              {sdg.description}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
