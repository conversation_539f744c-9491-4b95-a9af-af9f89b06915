import { Metadata } from "next";
import HeroSection from "@/components/home/<USER>";
import EmpoweringSection from "@/components/home/<USER>";
import SDGFocusAreas from "@/components/home/<USER>";
import ImpactSection from "@/components/home/<USER>";
import SuccessStorySection from "@/components/home/<USER>";
import PartnersSection from "@/components/home/<USER>";
import CallToActionSection from "@/components/home/<USER>";
import { generateSEOMetadata } from "@/lib/seo";
// import NewsletterSection from "@/components/home/<USER>";

export const metadata: Metadata = generateSEOMetadata({
  title: "GirlCode Movement | Empowering Women Through Technology & Mentorship",
  description:
    "Transform your future with GirlCode Movement. We provide digital skills training, career mentorship, and leadership development for women across Africa. Join thousands of women building successful careers in technology.",
  keywords: [
    "women in tech",
    "digital skills training",
    "career mentorship",
    "STEM education",
    "technology bootcamp",
    "women empowerment",
    "coding classes",
    "tech careers for women",
    "digital literacy",
    "entrepreneurship training",
  ],
  canonical: "/",
  ogImage: "/images/home-og.jpg",
});

const faqSchema = {
  "@context": "https://schema.org",
  "@type": "FAQPage",
  mainEntity: [
    {
      "@type": "Question",
      name: "What is GirlCode Movement?",
      acceptedAnswer: {
        "@type": "Answer",
        text: "GirlCode Movement is a nonprofit organization that empowers young girls and women through digital skills training, career mentorship, and leadership development programs across Africa.",
      },
    },
    {
      "@type": "Question",
      name: "Who can join GirlCode programs?",
      acceptedAnswer: {
        "@type": "Answer",
        text: "Our programs are open to girls and women of all ages and backgrounds who are interested in learning technology skills and advancing their careers in STEM fields.",
      },
    },
    {
      "@type": "Question",
      name: "Are GirlCode programs free?",
      acceptedAnswer: {
        "@type": "Answer",
        text: "Yes, most of our programs are offered free of charge to ensure accessibility for all participants regardless of their economic background.",
      },
    },
  ],
};

export default function Home() {
  return (
    <div>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(faqSchema),
        }}
      />
      <HeroSection />
      {/* Visual separator */}
      <div className="border-b border-gray-100"></div>
      <div className="space-y-16 md:space-y-20">
        <PartnersSection />
        <EmpoweringSection />
        <SDGFocusAreas />
        <ImpactSection />
        <SuccessStorySection />
        <CallToActionSection />
      </div>
      {/* <NewsletterSection /> */}
    </div>
  );
}
