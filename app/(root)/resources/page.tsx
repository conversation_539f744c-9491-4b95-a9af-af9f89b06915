import { Metada<PERSON> } from "next";
import React from "react";
import HeroS<PERSON><PERSON> from "@/components/resources/HeroSection";
import ResourceCategories from "@/components/resources/ResourceCategories";
import CodingResources from "@/components/resources/CodingResources";
import ThreeDPrintingResources from "@/components/resources/ThreeDPrintingResources";
import Teen<PERSON>elfHelp from "@/components/resources/TeenSelfHelp";
import CommunityShowcase from "@/components/resources/CommunityShowcase";
import WorkshopCTA from "@/components/resources/WorkshopCTA";
import ResourcesRequest from "@/components/resources/ResourcesRequest";
import { generateSEOMetadata, generateBreadcrumbSchema } from "@/lib/seo";

export const metadata: Metadata = generateSEOMetadata({
  title: "Resources - Free Learning Materials & Tools for Women in Tech",
  description: "Access free coding tutorials, 3D printing guides, teen self-help handbook, and comprehensive learning resources. Download materials, explore community projects, and find tools to advance your technology career.",
  keywords: [
    "free coding resources",
    "3D printing tutorials",
    "teen self-help handbook",
    "programming guides",
    "tech learning materials",
    "coding tutorials for beginners",
    "women in tech resources",
    "digital skills resources",
    "educational materials",
    "career development tools",
    "mental health resources",
    "technology guides"
  ],
  canonical: "/resources",
});

export default function resources() {
  return (
    <main className="min-h-screen bg-white">
      <HeroSection />
      {/* <ResourcesRequest /> */}
      {/* <ResourceCategories /> */}
      <CodingResources />
      <ThreeDPrintingResources />
      <TeenSelfHelp />
      {/* <CommunityShowcase /> */}
      {/* <WorkshopCTA /> */}
    </main>
  );
}
