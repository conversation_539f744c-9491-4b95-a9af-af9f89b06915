import { Metada<PERSON> } from "next";
import React from "react";
import HeroSection from "@/components/getinvolved/HeroSection";
import InvolvementOptions from "@/components/getinvolved/InvolvementOptions";
// import ImpactTestimonials from "@/components/getinvolved/ImpactTestimonials";
// import ContactInformation from "@/components/getinvolved/ContactInformation";
import ContactForm from "@/components/getinvolved/ContactForm";
import { generateSEOMetadata, generateBreadcrumbSchema } from "@/lib/seo";

export const metadata: Metadata = generateSEOMetadata({
  title: "Get Involved - Volunteer, Partner, & Support Women in Tech",
  description: "Join GirlCode Movement as a volunteer, mentor, partner, or donor. Discover multiple ways to support women's empowerment in technology and make a meaningful impact in communities across Africa.",
  keywords: [
    "volunteer opportunities",
    "mentorship programs",
    "partnership opportunities",
    "donate to women in tech",
    "support gender equality",
    "volunteer with nonprofits",
    "tech mentoring",
    "community involvement",
    "women empowerment support",
    "volunteer Kenya",
    "technology volunteering",
    "social impact volunteering"
  ],
  canonical: "/get-involved",
});

export default function getinvolved() {
  return (
    <main className="min-h-screen bg-white">
      <HeroSection />
      <InvolvementOptions />
      <ContactForm />
      {/* <ImpactTestimonials /> */}
      {/* <ContactInformation /> */}
    </main>
  );
}
