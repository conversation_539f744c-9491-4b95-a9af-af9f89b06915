"use client";

import Link from "next/link";

export default function NotFound() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-white p-4">
      <div className="w-full max-w-lg text-center">
        <div
          className="h-80 bg-contain bg-center bg-no-repeat mb-4"
          style={{
            backgroundImage:
              "url(https://cdn.dribbble.com/users/285475/screenshots/2083086/dribbble_1.gif)",
          }}
        >
          <h1 className="text-8xl font-bold text-pink-600">404</h1>
        </div>

        <div className="-mt-12">
          <h2 className="text-2xl font-bold text-gray-800 mb-2">
            Oops! Page Not Found
          </h2>

          <p className="text-gray-600 mb-6">
            Page not found! Our engineers at Girl
            <span className="text-pink-600">Code</span> are hard at work
            improving the website.
          </p>

          <div className="space-x-4">
            <button
              onClick={() => window.history.back()}
              className="inline-block px-6 py-3 bg-pink-600 hover:bg-pink-700 text-white font-medium rounded-md transition-colors duration-200"
            >
              Go Back
            </button>

            <Link
              href="/"
              className="inline-block px-6 py-3 bg-white border-2 border-pink-600 text-pink-600 hover:bg-pink-50 font-medium rounded-md transition-colors duration-200"
            >
              Go to Home
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
