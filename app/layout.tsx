import type { <PERSON><PERSON><PERSON>, <PERSON><PERSON> } from "next";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>_Dots,
  Montser<PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
} from "next/font/google";
import "./globals.css";
import { Providers } from "./providers";
import { Toaster } from "sonner";
import { organizationSchema } from "@/lib/seo";

const raleway = Raleway({
  variable: "--font-raleway",
  subsets: ["latin"],
  weight: "variable",
  display: "swap",
});

const ralewayDots = Raleway_Dots({
  variable: "--font-raleway-dots",
  subsets: ["latin"],
  weight: "400",
  display: "swap",
});

const montserrat = Montserrat({
  variable: "--font-montserrat",
  subsets: ["latin"],
  weight: "variable",
  display: "swap",
});

const poppins = Poppins({
  variable: "--font-poppins",
  subsets: ["latin"],
  weight: ["100", "200", "300", "400", "500", "600", "700", "800", "900"],
  display: "swap",
});

const lora = Lora({
  variable: "--font-lora",
  subsets: ["latin"],
  weight: "variable",
  display: "swap",
});

const roboto = Roboto({
  variable: "--font-roboto",
  subsets: ["latin"],
  weight: ["100", "300", "400", "500", "700", "900"],
  display: "swap",
});

export const metadata: Metadata = {
  metadataBase: new URL("https://girlcodemovement.org"),
  title: {
    template: "%s | GirlCode Movement",
    default: "GirlCode Movement | Empowering Women Through Technology & Mentorship",
  },
  description:
    "GirlCode Movement empowers young girls and women across Africa through digital skills training, career mentorship, and leadership development programs. Join our mission to bridge the gender gap in technology and create sustainable opportunities for women in STEM.",
  applicationName: "GirlCode Movement",
  authors: [{ name: "GirlCode Movement", url: "https://girlcodemovement.org" }],
  creator: "GirlCode Movement",
  publisher: "GirlCode Movement",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  keywords: [
    "GirlCode Movement",
    "women in technology",
    "digital skills training",
    "STEM education for girls",
    "career mentorship",
    "gender equality in tech",
    "women empowerment",
    "coding for girls",
    "technology education",
    "SDG goals",
    "sustainable development",
    "nonprofit organization",
    "Kenya",
    "Africa",
    "women leadership",
    "tech skills",
    "programming training",
    "digital literacy",
    "entrepreneurship for women",
    "innovation",
  ],
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
  openGraph: {
    type: "website",
    locale: "en_US",
    url: "https://girlcodemovement.org",
    siteName: "GirlCode Movement",
    title: "GirlCode Movement | Empowering Women Through Technology & Mentorship",
    description:
      "Join the GirlCode Movement: Transforming lives through technology education, mentorship, and leadership development for women across Africa.",
    images: [
      {
        url: "/images/og-image.jpg",
        width: 1200,
        height: 630,
        alt: "GirlCode Movement - Empowering Women in Technology",
        type: "image/jpeg",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "GirlCode Movement | Empowering Women Through Technology & Mentorship",
    description:
      "Transforming lives through technology education, mentorship, and leadership development for women across Africa.",
    creator: "@girlcode",
    site: "@girlcode",
    images: ["/images/twitter-image.jpg"],
  },
  verification: {
    google: "your-google-verification-code", // Replace with actual verification code
    yandex: "your-yandex-verification-code", // Replace with actual verification code
    yahoo: "your-yahoo-verification-code", // Replace with actual verification code
    other: {
      "msvalidate.01": "your-bing-verification-code", // Replace with actual verification code
    },
  },
  alternates: {
    canonical: "https://girlcodemovement.org",
  },
  category: "education",
};

export const viewport: Viewport = {
  themeColor: [
    { media: "(prefers-color-scheme: light)", color: "#FF0066" },
    { media: "(prefers-color-scheme: dark)", color: "#FF0066" },
  ],
  colorScheme: "light dark",
  width: "device-width",
  initialScale: 1,
  maximumScale: 5,
  userScalable: true,
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning={true}>
      <head>
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(organizationSchema),
          }}
        />
        <link rel="icon" href="/favicon.ico" sizes="any" />
        <link rel="icon" href="/icon.svg" type="image/svg+xml" />
        <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
        <link rel="manifest" href="/manifest.json" />
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="" />
        <meta name="format-detection" content="telephone=no, date=no, email=no, address=no" />
      </head>
      <body
        className={`${raleway.variable} ${ralewayDots.variable} ${montserrat.variable} ${poppins.variable} ${lora.variable} ${roboto.variable} antialiased overflow-x-hidden`}
      >
        <Providers>{children}</Providers>
        <Toaster />
      </body>
    </html>
  );
}
